-- Saudi Core Server Main
-- الخادم الرئيسي للنظام السعودي

print('^2[Saudi Core] ^7Saudi Arabia core system loading...')
print('^2[النظام السعودي] ^7جاري تحميل النظام الأساسي السعودي...')

-- Initialize Saudi Core
Citizen.CreateThread(function()
    Citizen.Wait(1000)
    
    print('^2[Saudi Core] ^7Saudi Arabia core system loaded successfully!')
    print('^2[النظام السعودي] ^7تم تحميل النظام الأساسي السعودي بنجاح!')
    
    -- Send welcome message to all players
    TriggerClientEvent('chat:addMessage', -1, {
        color = {0, 108, 53},
        args = {'[السيرفر السعودي]', 'تم تحميل النظام الأساسي السعودي'}
    })
end)

-- Handle player connecting
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    print('^3[Saudi Core] ^7Player connecting: ' .. name)
    print('^3[النظام السعودي] ^7اللاعب يتصل: ' .. name)
end)

-- Handle player joined
AddEventHandler('playerJoining', function()
    local source = source
    local playerName = GetPlayerName(source)
    
    print('^2[Saudi Core] ^7Player joined: ' .. playerName)
    print('^2[النظام السعودي] ^7انضم اللاعب: ' .. playerName)
    
    -- Send welcome message after 3 seconds
    Citizen.SetTimeout(3000, function()
        TriggerClientEvent('chat:addMessage', source, {
            color = {0, 108, 53},
            args = {'[مرحباً]', 'أهلاً وسهلاً بك في سيرفر المملكة العربية السعودية'}
        })
        
        TriggerClientEvent('chat:addMessage', source, {
            color = {0, 108, 53},
            args = {'[Welcome]', 'Welcome to Saudi Arabia Roleplay Server'}
        })
    end)
end)

-- Saudi Core Commands
RegisterCommand('saudi', function(source, args, rawCommand)
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 108, 53},
        args = {'[السعودية]', 'المملكة العربية السعودية - أرض الحرمين الشريفين'}
    })
end, false)

RegisterCommand('time', function(source, args, rawCommand)
    local currentTime = os.date('%H:%M:%S', os.time())
    local currentDate = os.date('%Y-%m-%d', os.time())
    
    TriggerClientEvent('chat:addMessage', source, {
        color = {255, 255, 255},
        args = {'[الوقت]', 'التوقيت الحالي: ' .. currentTime .. ' - التاريخ: ' .. currentDate}
    })
end, false)

-- Export functions
exports('GetSaudiTime', function()
    return os.date('%H:%M:%S', os.time())
end)

exports('GetSaudiDate', function()
    return os.date('%Y-%m-%d', os.time())
end)
