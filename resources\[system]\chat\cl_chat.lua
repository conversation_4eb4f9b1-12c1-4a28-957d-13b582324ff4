-- Chat Client for Saudi FiveM Server
-- عميل الدردشة للسيرفر السعودي

local chatInputActive = false
local chatInputActivating = false
local chatHidden = false

-- Initialize chat
Citizen.CreateThread(function()
    SetTextChatEnabled(false)
    SetNuiFocus(false, false)
    
    print('^2[Chat Client] ^7Saudi chat system initialized')
    print('^2[عميل الدردشة] ^7تم تهيئة نظام الدردشة السعودي')
end)

-- Add message function
function addMessage(message)
    SendNUIMessage({
        type = 'addMessage',
        args = message.args or {'', ''},
        color = message.color or {255, 255, 255}
    })
end

-- Add suggestion function
function addSuggestion(name, help, params)
    TriggerEvent('chat:addSuggestion', '/' .. name, help, params or {})
end

-- Remove suggestion function
function removeSuggestion(name)
    TriggerEvent('chat:removeSuggestion', '/' .. name)
end

-- Handle chat toggle
RegisterCommand('chatmode', function()
    if not chatInputActive then
        chatInputActivating = true
        SendNUIMessage({
            type = 'ON_OPEN'
        })
        SetNuiFocus(true, true)
        chatInputActive = true
    end
end, false)

-- Register key mapping for chat
RegisterKeyMapping('chatmode', 'Open Chat', 'keyboard', 't')

-- Handle NUI callbacks
RegisterNUICallback('chatResult', function(data, cb)
    chatInputActive = false
    SetNuiFocus(false, false)
    
    if not data.cancel then
        local message = data.message
        if message and string.len(message) > 0 then
            TriggerServerEvent('chat:server:sendMessage', message)
        end
    end
    
    SendNUIMessage({
        type = 'ON_CLOSE'
    })
    
    cb('ok')
end)

-- Handle chat messages
RegisterNetEvent('chat:addMessage')
AddEventHandler('chat:addMessage', function(message)
    addMessage(message)
end)

-- Handle suggestions
RegisterNetEvent('chat:addSuggestion')
AddEventHandler('chat:addSuggestion', function(name, help, params)
    -- Add command suggestion logic here
end)

RegisterNetEvent('chat:removeSuggestion')
AddEventHandler('chat:removeSuggestion', function(name)
    -- Remove command suggestion logic here
end)

-- Saudi specific chat commands
RegisterCommand('help', function()
    addMessage({
        color = {0, 108, 53},
        args = {'[مساعدة]', 'الأوامر المتاحة: /say /me /ooc /arabic /prayer'}
    })
    addMessage({
        color = {0, 108, 53},
        args = {'[Help]', 'Available commands: /say /me /ooc /arabic /prayer'}
    })
end, false)

-- Export functions
exports('addMessage', addMessage)
exports('addSuggestion', addSuggestion)
exports('removeSuggestion', removeSuggestion)
