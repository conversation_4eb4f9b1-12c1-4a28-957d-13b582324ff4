-- Saudi Core Client Main
-- العميل الرئيسي للنظام السعودي

print('^2[Saudi Core Client] ^7Saudi Arabia client system loading...')
print('^2[عميل النظام السعودي] ^7جاري تحميل نظام العميل السعودي...')

-- Initialize Saudi Core Client
Citizen.CreateThread(function()
    Citizen.Wait(1000)
    
    print('^2[Saudi Core Client] ^7Saudi Arabia client system loaded!')
    print('^2[عميل النظام السعودي] ^7تم تحميل نظام العميل السعودي!')
end)

-- Saudi Core Client Functions
function ShowSaudiNotification(message)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(message)
    DrawNotification(0, 1)
end

-- Export client functions
exports('ShowSaudiNotification', ShowSaudiNotification)
