// Saudi Chat Script - سكريبت الدردشة السعودية

let chatContainer = document.getElementById('chat-messages');
let chatInput = document.getElementById('chat-input');
let chatInputContainer = document.getElementById('chat-input-container');
let isInputActive = false;

// Saudi chat configuration
const saudiConfig = {
    maxMessages: 100,
    fadeTime: 10000,
    colors: {
        system: '#006C35',
        player: '#FFFFFF',
        ooc: '#808080',
        me: '#FFC20E',
        arabic: '#006C35'
    }
};

// Initialize chat
window.addEventListener('message', function(event) {
    if (event.data.type === 'ON_OPEN') {
        chatInputContainer.style.display = 'block';
        chatInput.focus();
        isInputActive = true;
    } else if (event.data.type === 'ON_CLOSE') {
        chatInputContainer.style.display = 'none';
        chatInput.blur();
        isInputActive = false;
    }
});

// Handle chat input
chatInput.addEventListener('keydown', function(event) {
    if (event.key === 'Enter') {
        let message = chatInput.value.trim();
        if (message) {
            // Send message to FiveM
            fetch(`https://${GetParentResourceName()}/chatResult`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                },
                body: JSON.stringify({
                    message: message
                })
            });
        }
        chatInput.value = '';
    } else if (event.key === 'Escape') {
        // Close chat
        fetch(`https://${GetParentResourceName()}/chatResult`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
            },
            body: JSON.stringify({
                cancel: true
            })
        });
    }
});

// Add message to chat
function addMessage(author, message, color) {
    let messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message';
    
    if (author) {
        let authorSpan = document.createElement('span');
        authorSpan.className = 'chat-author';
        authorSpan.textContent = author + ':';
        authorSpan.style.color = color || saudiConfig.colors.player;
        messageDiv.appendChild(authorSpan);
    }
    
    let textSpan = document.createElement('span');
    textSpan.className = 'chat-text';
    textSpan.textContent = ' ' + message;
    messageDiv.appendChild(textSpan);
    
    // Add timestamp
    let timestamp = new Date().toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
    });
    let timeSpan = document.createElement('span');
    timeSpan.className = 'chat-timestamp';
    timeSpan.textContent = ' [' + timestamp + ']';
    messageDiv.appendChild(timeSpan);
    
    chatContainer.appendChild(messageDiv);
    
    // Remove old messages
    while (chatContainer.children.length > saudiConfig.maxMessages) {
        chatContainer.removeChild(chatContainer.firstChild);
    }
    
    // Scroll to bottom
    chatContainer.scrollTop = chatContainer.scrollHeight;
    
    // Auto-fade message
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.opacity = '0.5';
        }
    }, saudiConfig.fadeTime);
}

// Handle messages from FiveM
window.addEventListener('message', function(event) {
    if (event.data.type === 'addMessage') {
        let data = event.data;
        addMessage(data.args[0], data.args[1], data.color);
    }
});

// Saudi welcome message
document.addEventListener('DOMContentLoaded', function() {
    addMessage('السيرفر السعودي', 'أهلاً وسهلاً بك في سيرفر المملكة العربية السعودية', saudiConfig.colors.system);
    addMessage('Saudi Server', 'Welcome to Saudi Arabia Roleplay Server', saudiConfig.colors.system);
});

// Helper function to get parent resource name
function GetParentResourceName() {
    return 'chat';
}
