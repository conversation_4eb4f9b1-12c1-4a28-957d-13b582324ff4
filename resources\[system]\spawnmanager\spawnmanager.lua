-- Spawn Manager Client for Saudi FiveM Server
-- مدير الإحياء للعميل - السيرفر السعودي

local spawnPoints = {}
local autoSpawn = false
local autoSpawnCallback = nil

-- Saudi spawn points
local saudiSpawns = {
    {x = -1037.0, y = -2737.0, z = 20.2, heading = 0.0}, -- Riyadh
    {x = 1400.0, y = 1100.0, z = 114.0, heading = 0.0}, -- Jeddah
    {x = -3000.0, y = 1000.0, z = 12.0, heading = 0.0}, -- Dammam
    {x = 2000.0, y = 3000.0, z = 48.0, heading = 0.0}, -- Mecca
    {x = -2000.0, y = 2500.0, z = 33.0, heading = 0.0} -- Medina
}

-- Initialize spawn points
Citizen.CreateThread(function()
    for _, spawn in ipairs(saudiSpawns) do
        addSpawnPoint(spawn)
    end
    
    print('^2[Spawn Manager] ^7Saudi spawn points loaded')
    print('^2[مدير الإحياء] ^7تم تحميل نقاط الإحياء السعودية')
end)

-- Add spawn point
function addSpawnPoint(spawn)
    table.insert(spawnPoints, spawn)
end

-- Remove spawn point
function removeSpawnPoint(spawn)
    for i, point in ipairs(spawnPoints) do
        if point.x == spawn.x and point.y == spawn.y then
            table.remove(spawnPoints, i)
            break
        end
    end
end

-- Load spawns
function loadSpawns()
    return spawnPoints
end

-- Set auto spawn
function setAutoSpawn(enabled)
    autoSpawn = enabled
end

-- Set auto spawn callback
function setAutoSpawnCallback(callback)
    autoSpawnCallback = callback
end

-- Spawn player
function spawnPlayer(spawn, callback)
    if spawn then
        RequestCollisionAtCoord(spawn.x, spawn.y, spawn.z)
        
        local model = GetHashKey('mp_m_freemode_01')
        RequestModel(model)
        
        while not HasModelLoaded(model) do
            Citizen.Wait(0)
        end
        
        SetPlayerModel(PlayerId(), model)
        SetModelAsNoLongerNeeded(model)
        
        SetEntityCoords(PlayerPedId(), spawn.x, spawn.y, spawn.z, false, false, false, true)
        SetEntityHeading(PlayerPedId(), spawn.heading or 0.0)
        
        NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading or 0.0, true, false)
        
        ClearPedTasksImmediately(PlayerPedId())
        RemoveAllPedWeapons(PlayerPedId(), true)
        ClearPlayerWantedLevel(PlayerId())
        
        if callback then
            callback()
        end
        
        print('^2[Spawn Manager] ^7Player spawned in Saudi Arabia')
        print('^2[مدير الإحياء] ^7تم إحياء اللاعب في المملكة العربية السعودية')
    end
end

-- Force respawn
function forceRespawn()
    if #spawnPoints > 0 then
        local randomSpawn = spawnPoints[math.random(#spawnPoints)]
        spawnPlayer(randomSpawn)
    end
end

-- Handle player death
AddEventHandler('baseevents:onPlayerDied', function()
    if autoSpawn then
        Citizen.CreateThread(function()
            Citizen.Wait(5000) -- Wait 5 seconds
            if autoSpawnCallback then
                autoSpawnCallback()
            else
                forceRespawn()
            end
        end)
    end
end)

-- Handle player killed
AddEventHandler('baseevents:onPlayerKilled', function()
    if autoSpawn then
        Citizen.CreateThread(function()
            Citizen.Wait(5000) -- Wait 5 seconds
            if autoSpawnCallback then
                autoSpawnCallback()
            else
                forceRespawn()
            end
        end)
    end
end)
