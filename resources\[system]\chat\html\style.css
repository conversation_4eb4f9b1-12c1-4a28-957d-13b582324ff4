/* Saudi Chat Styles - أنماط الدردشة السعودية */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', <PERSON><PERSON>, sans-serif;
    direction: rtl;
    text-align: right;
    background: transparent;
    overflow: hidden;
}

#chat-container {
    position: fixed;
    top: 10px;
    right: 10px;
    width: 500px;
    height: 400px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
}

#chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 5px;
    scrollbar-width: thin;
    scrollbar-color: #006C35 transparent;
}

#chat-messages::-webkit-scrollbar {
    width: 6px;
}

#chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

#chat-messages::-webkit-scrollbar-thumb {
    background: #006C35;
    border-radius: 3px;
}

.chat-message {
    margin-bottom: 8px;
    padding: 5px 8px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    word-wrap: break-word;
    animation: fadeIn 0.3s ease-in;
}

.chat-message.system {
    background: rgba(0, 108, 53, 0.3);
    border-left: 3px solid #006C35;
}

.chat-message.ooc {
    background: rgba(128, 128, 128, 0.3);
    border-left: 3px solid #808080;
}

.chat-message.me {
    background: rgba(255, 194, 14, 0.3);
    border-left: 3px solid #FFC20E;
    font-style: italic;
}

.chat-message.arabic {
    background: rgba(0, 108, 53, 0.3);
    border-left: 3px solid #006C35;
}

.chat-author {
    font-weight: bold;
    color: #FFD700;
    margin-left: 5px;
}

.chat-text {
    color: #FFFFFF;
    line-height: 1.4;
}

#chat-input-container {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

#chat-input {
    width: 100%;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    color: #FFFFFF;
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
    font-size: 14px;
    direction: rtl;
    text-align: right;
}

#chat-input:focus {
    outline: none;
    border-color: #006C35;
    background: rgba(255, 255, 255, 0.15);
}

#chat-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
    direction: rtl;
    text-align: right;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Saudi flag colors theme */
.saudi-green {
    color: #006C35;
}

.saudi-white {
    color: #FFFFFF;
}

.saudi-gold {
    color: #FFD700;
}

/* Responsive design */
@media (max-width: 768px) {
    #chat-container {
        width: 90%;
        right: 5%;
        height: 300px;
    }
}

/* Arabic text improvements */
.arabic-text {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

/* Command suggestions */
.chat-suggestion {
    background: rgba(0, 108, 53, 0.2);
    border: 1px solid #006C35;
    padding: 3px 6px;
    margin: 2px;
    border-radius: 3px;
    font-size: 12px;
    color: #FFD700;
    display: inline-block;
}

/* Timestamp */
.chat-timestamp {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
    margin-left: 5px;
}

/* Player name colors */
.player-name {
    font-weight: bold;
}

.player-name.police {
    color: #0066CC;
}

.player-name.ambulance {
    color: #FF6B6B;
}

.player-name.fire {
    color: #FF4444;
}

.player-name.taxi {
    color: #FFDD44;
}

.player-name.aramco {
    color: #006C35;
}

/* Scrollbar for Arabic */
#chat-messages {
    scrollbar-width: thin;
    scrollbar-color: #006C35 transparent;
}

/* Hide scrollbar when not needed */
#chat-messages:not(:hover) {
    scrollbar-width: none;
}

#chat-messages:not(:hover)::-webkit-scrollbar {
    display: none;
}
