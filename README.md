# Saudi FiveM Server - سيرفر فايف ام السعودي

## نظرة عامة | Overview

هذا سيرفر FiveM مخصص للمملكة العربية السعودية مع توطين كامل وميزات سعودية أصيلة.

This is a FiveM server specifically designed for Saudi Arabia with full localization and authentic Saudi features.

## المميزات | Features

### 🇸🇦 التوطين السعودي | Saudi Localization
- واجهة مستخدم عربية كاملة | Full Arabic UI
- أسماء مدن سعودية | Saudi city names
- عملة الريال السعودي | Saudi Riyal currency
- أوقات الصلاة | Prayer times
- التقويم الهجري | Hijri calendar

### 💼 الوظائف السعودية | Saudi Jobs
- شرطة | Police
- إسعاف | Ambulance  
- إطفاء | Fire Department
- تاكسي | Taxi
- أرامكو | Saudi Aramco
- سابك | SABIC
- وظائف حكومية أخرى | Other government jobs

### 🚗 المركبات السعودية | Saudi Vehicles
- لوحات أرقام سعودية | Saudi license plates
- محطات وقود أرامكو | Aramco fuel stations
- معارض سيارات سعودية | Saudi car dealerships
- نظام تسجيل المركبات | Vehicle registration system

### 🏛️ الخدمات الحكومية | Government Services
- نظام الهوية الوطنية | National ID system
- نظام الإقامة | Iqama system
- رخص القيادة السعودية | Saudi driving licenses
- الخدمات المصرفية | Banking services
- نظام الزكاة | Zakat system

### 🏥 الخدمات الأساسية | Essential Services
- نظام صحي مجاني | Free healthcare system
- نظام تعليمي | Education system
- التأمين الاجتماعي | Social insurance
- خدمات الطوارئ | Emergency services

## متطلبات التشغيل | Requirements

### الخادم | Server Requirements
- Windows Server 2016+ أو Linux
- 4GB RAM كحد أدنى | Minimum 4GB RAM
- 50GB مساحة تخزين | 50GB storage space
- اتصال إنترنت مستقر | Stable internet connection

### قاعدة البيانات | Database
- MongoDB Atlas (مُضمن) | MongoDB Atlas (included)
- رابط الاتصال: `mongodb+srv://whm:<EMAIL>/`

### FiveM Server Files
- تحميل ملفات FiveM Server من | Download FiveM Server files from:
- https://runtime.fivem.net/artifacts/fivem/build_server_windows/master/

## التثبيت | Installation

### 1. تحميل ملفات FiveM Server
```bash
# قم بتحميل وفك ضغط ملفات FiveM Server في هذا المجلد
# Download and extract FiveM Server files to this folder
```

### 2. إعداد المفاتيح | Setup Keys
قم بتعديل الملفات التالية وإضافة مفاتيحك:
Edit the following files and add your keys:

**server.cfg:**
```cfg
sv_licenseKey "YOUR_LICENSE_KEY_HERE"
steam_webApiKey "YOUR_STEAM_API_KEY_HERE"
rcon_password "YOUR_RCON_PASSWORD_HERE"
```

### 3. تشغيل السيرفر | Start Server
```bash
# Windows
start.bat

# Linux
./start.sh
```

## الإعدادات | Configuration

### إعدادات السيرفر | Server Settings
- **اسم السيرفر**: سيرفر المملكة العربية السعودية
- **الحد الأقصى للاعبين**: 64
- **المنطقة الزمنية**: Asia/Riyadh
- **اللغة**: العربية (ar-SA)

### إعدادات قاعدة البيانات | Database Settings
```cfg
mongodb_connection_string = "mongodb+srv://whm:<EMAIL>/"
mongodb_database = "saudi_fivem_server"
```

### المدن السعودية | Saudi Cities
- الرياض | Riyadh
- جدة | Jeddah  
- الدمام | Dammam
- مكة المكرمة | Mecca
- المدينة المنورة | Medina
- الطائف | Taif
- بريدة | Buraidah
- تبوك | Tabuk
- حائل | Hail
- أبها | Abha

## أرقام الطوارئ | Emergency Numbers

| الخدمة | Service | الرقم | Number |
|---------|---------|-------|---------|
| الشرطة | Police | 999 | 999 |
| الإسعاف | Ambulance | 997 | 997 |
| الإطفاء | Fire | 998 | 998 |
| المرور | Traffic | 993 | 993 |

## الأوامر | Commands

### أوامر عامة | General Commands
- `/help` - عرض المساعدة | Show help
- `/me [action]` - إجراء شخصي | Personal action
- `/do [description]` - وصف الحالة | State description
- `/ooc [message]` - رسالة خارج الشخصية | Out of character message

### أوامر الوظائف | Job Commands
- `/duty` - بدء/إنهاء الخدمة | Start/end duty
- `/radio [frequency]` - تغيير تردد الراديو | Change radio frequency
- `/backup` - طلب النجدة (شرطة) | Request backup (police)

### أوامر المركبات | Vehicle Commands
- `/car [model]` - استدعاء مركبة | Spawn vehicle
- `/dv` - حذف المركبة | Delete vehicle
- `/engine` - تشغيل/إيقاف المحرك | Start/stop engine
- `/fuel` - عرض مستوى الوقود | Show fuel level

## الدعم الفني | Technical Support

### المشاكل الشائعة | Common Issues

**1. خطأ في الاتصال بقاعدة البيانات**
```
[ERROR] Database connection failed
```
**الحل**: تأكد من صحة رابط MongoDB في `database.cfg`

**2. خطأ في تحميل الموارد**
```
[ERROR] Failed to start resource
```
**الحل**: تأكد من وجود جميع الملفات المطلوبة

**3. مشكلة في أوقات الصلاة**
```
[WARNING] Prayer times not updating
```
**الحل**: تحقق من إعدادات المنطقة الزمنية

### سجلات الأخطاء | Error Logs
- سجلات الخادم: `server.log`
- سجلات قاعدة البيانات: `database.log`
- سجلات الموارد: `resources.log`

## المساهمة | Contributing

نرحب بمساهماتكم في تطوير السيرفر السعودي!
We welcome your contributions to develop the Saudi server!

### كيفية المساهمة | How to Contribute
1. Fork المشروع | Fork the project
2. إنشاء فرع جديد | Create a new branch
3. إضافة التحسينات | Add improvements
4. إرسال Pull Request | Submit Pull Request

## الترخيص | License

هذا المشروع مرخص تحت رخصة MIT
This project is licensed under the MIT License

## الاتصال | Contact

- **Discord**: [رابط الديسكورد]
- **Email**: <EMAIL>
- **Website**: www.saudifivem.com

## شكر خاص | Special Thanks

- فريق تطوير FiveM | FiveM Development Team
- مجتمع FiveM العربي | Arabic FiveM Community
- المطورين السعوديين | Saudi Developers

---

**ملاحظة**: هذا السيرفر مصمم خصيصاً للمجتمع السعودي ويحتوي على عناصر ثقافية ودينية سعودية أصيلة.

**Note**: This server is specifically designed for the Saudi community and contains authentic Saudi cultural and religious elements.

## إصدارات التحديث | Update Versions

### الإصدار 1.0.0 | Version 1.0.0
- إطلاق السيرفر السعودي الأول | First Saudi server release
- نظام قاعدة البيانات MongoDB | MongoDB database system
- الواجهة العربية الكاملة | Full Arabic interface
- الوظائف السعودية الأساسية | Basic Saudi jobs
- نظام المركبات السعودية | Saudi vehicles system

### التحديثات القادمة | Upcoming Updates
- نظام الحج والعمرة | Hajj and Umrah system
- المزيد من المدن السعودية | More Saudi cities
- نظام الأعمال التجارية | Business system
- تطبيق أبشر المحاكي | Absher app simulation
- نظام التعليم الجامعي | University education system

---

**🇸🇦 صُنع بحب في المملكة العربية السعودية | Made with ❤️ in Saudi Arabia 🇸🇦**
