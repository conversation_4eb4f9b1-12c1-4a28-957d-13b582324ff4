[{"ctx": {"idx": 0, "type": "Entity"}, "getter": {"returnType": "Vector3", "returnArgStart": 1, "name": "GET_ENTITY_COORDS"}, "name": "SET_ENTITY_COORDS", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "hash": "0x06843DA7060A026B"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_ENTITY_ROTATION", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "BOOL"}], "hash": "0x8524A8B0171D5E07"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_ENTITY_VELOCITY", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}, {"type": "float"}, {"type": "float"}], "hash": "0x1C99BB7B6E96D16F"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_ENTITY_HEADING", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}], "hash": "0x8E2530AA8ADA980E"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "FREEZE_ENTITY_POSITION", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "BOOL"}], "hash": "0x428CA6DBD1094446"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "APPLY_FORCE_TO_ENTITY", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "hash": "0xC5F68BE9613E2D18"}, {"ctx": {"idx": 0, "type": "Player"}, "name": "SET_PLAYER_MODEL", "type": "ctx", "args": [{"translate": true, "type": "Player"}, {"type": "Hash"}], "hash": "0x00A1CADD00108836"}, {"ctx": {"idx": 0, "type": "Player"}, "name": "SET_PLAYER_CONTROL", "type": "ctx", "args": [{"translate": true, "type": "Player"}, {"type": "BOOL"}, {"type": "int"}], "hash": "0x8D32347D6D4C40A2"}, {"ctx": {"idx": 0, "type": "Player"}, "name": "SET_PLAYER_INVINCIBLE", "type": "ctx", "args": [{"translate": true, "type": "Player"}, {"type": "BOOL"}], "hash": "0x239528EACDC3E7DE"}, {"ctx": {"idx": 0, "type": "Player"}, "name": "SET_PLAYER_WANTED_LEVEL", "type": "ctx", "args": [{"translate": true, "type": "Player"}, {"type": "int"}, {"type": "BOOL"}], "hash": "0x39FF19C64EF7DA5B"}, {"ctx": {"idx": 0, "type": "Player"}, "name": "CLEAR_PLAYER_WANTED_LEVEL", "type": "ctx", "args": [{"translate": true, "type": "Player"}], "hash": "0xB302540597885499"}, {"args": [{"type": "int"}, {"type": "Hash"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}], "type": "entity", "name": "CREATE_PED", "hash": "0xD49F9B0955C367DE"}, {"args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "Hash"}, {"type": "int"}, {"type": "BOOL"}, {"type": "BOOL"}], "type": "entity", "name": "CREATE_PED_INSIDE_VEHICLE", "hash": "0x7DD959874C1FD534"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "ADD_PED_DECORATION_FROM_HASHES", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "Hash"}, {"type": "Hash"}], "hash": "0x5F5D1665E352A839"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_INTO_VEHICLE", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0xF75B0D629E1C063D"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_HEAD_BLEND_DATA", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}], "hash": "0x9414E18B9434C2FE"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_HEAD_OVERLAY", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "float"}], "hash": "0x48F44967FA05CC1E"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "_SET_PED_HAIR_COLOR", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}], "hash": "0x4CFFC65454C93A49"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "_SET_PED_HEAD_OVERLAY_COLOR", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}], "hash": "0x497BF74A7B9CB952"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "_SET_PED_EYE_COLOR", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0x50B56988B170AFDF"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "_SET_PED_FACE_FEATURE", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "float"}], "hash": "0x71A5C1DBA060049E"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_DEFAULT_COMPONENT_VARIATION", "type": "ctx", "args": [{"translate": true, "type": "Entity"}], "hash": "0x45EEE61580806D63"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_RANDOM_COMPONENT_VARIATION", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0xC8A9481A01E63C28"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_COMPONENT_VARIATION", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "int"}], "hash": "0x262B14F48D29DE80"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "CLEAR_PED_PROP", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0x0943E5B8E078E76E"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_RANDOM_PROPS", "type": "ctx", "args": [{"translate": true, "type": "Entity"}], "hash": "0xC44AA05345C992C6"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_PROP_INDEX", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "BOOL"}], "hash": "0x93376B65A266EB5F"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_ARMOUR", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0xCEA04D83135264CC"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_CAN_RAGDOLL", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "BOOL"}], "hash": "0xB128377056A54E2A"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_CONFIG_FLAG", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "BOOL"}], "hash": "0x1913FE4CBF41C463"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_RESET_FLAG", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "BOOL"}], "hash": "0xC1E8A365BF3B29F2"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_TO_RAGDOLL", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "hash": "0xAE99FB955581844A"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_TO_RAGDOLL_WITH_FALL", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "int"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}], "hash": "0xD76632D99E4966C8"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "GIVE_WEAPON_TO_PED", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "Hash"}, {"type": "int"}, {"type": "BOOL"}, {"type": "BOOL"}], "hash": "0xBF0FD6E56C964FCB"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "GIVE_WEAPON_COMPONENT_TO_PED", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "Hash"}, {"type": "Hash"}], "hash": "0xD966D51AA5B28BB9"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "REMOVE_WEAPON_FROM_PED", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "Hash"}], "hash": "0x4899CB088EDF59B8"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "REMOVE_ALL_PED_WEAPONS", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "BOOL"}], "hash": "0xF25DF915FA38C5F3"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "REMOVE_WEAPON_COMPONENT_FROM_PED", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "Hash"}, {"type": "Hash"}], "hash": "0x1E8BE90C74FB4C09"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_CURRENT_PED_WEAPON", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "Hash"}, {"type": "BOOL"}], "hash": "0xADF692B254977C0C"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_PED_AMMO", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "Hash"}, {"type": "int"}], "hash": "0x14E56BC5B5DB6A19"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_REACT_AND_FLEE_PED", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}], "hash": "0x72C896464915D1B1"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_SHOOT_AT_COORD", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "Hash"}], "hash": "0x46A6CC01E0826106"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_SHOOT_AT_ENTITY", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "Hash"}], "hash": "0x08DA95E8298AE772"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_COMBAT_PED", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}], "hash": "0xF166E48407BAC484"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_DRIVE_BY", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "BOOL"}, {"type": "Hash"}], "hash": "0x2F8AF0E82773A171"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_ENTER_VEHICLE", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "float"}, {"type": "int"}, {"type": "Any"}], "hash": "0xC20E50AA46D09CA8"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_WARP_PED_INTO_VEHICLE", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0x9A7D091411C5F684"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_HANDS_UP", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "BOOL"}], "hash": "0xF2EAB31979A7F910"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_PLAY_ANIM", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "char<PERSON><PERSON>"}, {"type": "char<PERSON><PERSON>"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "int"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "hash": "0xEA47FE3719165B94"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_PLAY_ANIM_ADVANCED", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "char<PERSON><PERSON>"}, {"type": "char<PERSON><PERSON>"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "Any"}, {"type": "float"}, {"type": "Any"}, {"type": "Any"}], "hash": "0x83CDB10EA29B370B"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "CLEAR_PED_TASKS", "type": "ctx", "args": [{"translate": true, "type": "Entity"}], "hash": "0xE1EF3C1216AFF2CD"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "CLEAR_PED_TASKS_IMMEDIATELY", "type": "ctx", "args": [{"translate": true, "type": "Entity"}], "hash": "0xAAA34F8A7CB32098"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "CLEAR_PED_SECONDARY_TASK", "type": "ctx", "args": [{"translate": true, "type": "Entity"}], "hash": "0x176CECF6F920D707"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_EVERYONE_LEAVE_VEHICLE", "type": "ctx", "args": [{"translate": true, "type": "Entity"}], "hash": "0x7F93691AB4B92272"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_LEAVE_ANY_VEHICLE", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}], "hash": "0x504D54DF3F6F2247"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_LEAVE_VEHICLE", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0xD3DBCE61A490BE02"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_GO_STRAIGHT_TO_COORD", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}, {"type": "float"}, {"type": "float"}], "hash": "0xD76B57B44F1E6F8B"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_GO_TO_COORD_ANY_MEANS", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "Any"}, {"type": "BOOL"}, {"type": "int"}, {"type": "float"}], "hash": "0x5BC448CB78FA3E88"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "TASK_GO_TO_ENTITY", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "int"}], "hash": "0x6A071245EB0D1882"}, {"args": [{"type": "Hash"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}], "type": "entity", "name": "CREATE_VEHICLE", "hash": "0xAF35D0D2583051B0"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_ALARM", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "BOOL"}], "hash": "0xCDE5E70C1DDB954C"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_BODY_HEALTH", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}], "hash": "0xB77D05AC8C78AADB"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_COLOURS", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}], "hash": "0x4F1D4BE3A7F24601"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_COLOUR_COMBINATION", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0x33E8CD3322E2FE31"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_CUSTOM_PRIMARY_COLOUR", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "int"}], "hash": "0x7141766F91D15BEA"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_CUSTOM_SECONDARY_COLOUR", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "int"}, {"type": "int"}], "hash": "0x36CED73BFED89754"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_DIRT_LEVEL", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "float"}], "hash": "0x79D3B596FE44EE8B"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_DOOR_BROKEN", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}, {"type": "BOOL"}], "hash": "0xD4D4F6A4AB575A33"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_NUMBER_PLATE_TEXT", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "char<PERSON><PERSON>"}], "hash": "0x95A88F0B409CDA47"}, {"ctx": {"idx": 0, "type": "Entity"}, "name": "SET_VEHICLE_DOORS_LOCKED", "type": "ctx", "args": [{"translate": true, "type": "Entity"}, {"type": "int"}], "hash": "0xB664292EAECF7FA6"}, {"args": [{"type": "Hash"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "type": "entity", "name": "CREATE_OBJECT", "hash": "0x509D5878EB39E842"}, {"args": [{"type": "Hash"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "BOOL"}, {"type": "BOOL"}, {"type": "BOOL"}], "type": "entity", "name": "CREATE_OBJECT_NO_OFFSET", "hash": "0x9A294B2138ABB884"}, {"args": [{"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}], "type": "object", "name": "_ADD_BLIP_FOR_AREA", "hash": "0xCE5D0E5E315DB238"}, {"args": [{"type": "float"}, {"type": "float"}, {"type": "float"}], "type": "object", "name": "ADD_BLIP_FOR_COORD", "hash": "0x5A039BB0BCA604B6"}, {"args": [{"type": "float"}, {"type": "float"}, {"type": "float"}, {"type": "float"}], "type": "object", "name": "ADD_BLIP_FOR_RADIUS", "hash": "0x46818D79B1F7499A"}, {"args": [{"translate": true, "type": "Entity"}], "type": "object", "name": "ADD_BLIP_FOR_ENTITY", "hash": "0x5CDE92C702A8FCE7"}, {"ctx": {"idx": 0, "type": "ObjRef"}, "getter": {"returnType": "int", "returnArgStart": 1, "name": "GET_BLIP_SPRITE"}, "name": "SET_BLIP_SPRITE", "type": "ctx", "args": [{"translate": true, "type": "ObjRef"}, {"type": "int"}], "hash": "0xDF735600A4696DAF"}, {"ctx": {"idx": 0, "type": "<PERSON><PERSON>j<PERSON><PERSON>"}, "name": "REMOVE_BLIP", "type": "ctx", "args": [{"translate": true, "type": "<PERSON><PERSON>j<PERSON><PERSON>"}], "hash": "0x86A652570E5F25DD"}]