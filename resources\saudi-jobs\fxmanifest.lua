fx_version 'cerulean'
game 'gta5'

name 'Saudi Jobs System'
description 'نظام الوظائف السعودية - Saudi Arabian Jobs System'
author 'Saudi FiveM Development Team'
version '1.0.0'

-- Client Scripts
client_scripts {
    'config.lua',
    'client/main.lua',
    'client/police.lua',
    'client/ambulance.lua',
    'client/fire.lua',
    'client/taxi.lua',
    'client/aramco.lua',
    'client/government.lua'
}

-- Server Scripts
server_scripts {
    '@mongodb/lib/mongodb.lua',
    'config.lua',
    'server/main.lua',
    'server/police.lua',
    'server/ambulance.lua',
    'server/fire.lua',
    'server/taxi.lua',
    'server/aramco.lua',
    'server/government.lua',
    'server/payroll.lua'
}

-- Shared Scripts
shared_scripts {
    'shared/utils.lua'
}

-- UI Files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/style.css',
    'html/script.js',
    'html/jobs/*'
}

-- Dependencies
dependencies {
    'saudi-core',
    'mongodb'
}

-- Saudi Jobs Configuration
saudi_jobs = {
    -- Government Jobs
    police = {
        name = 'شرطة',
        nameEN = 'Police',
        salary = 3000,
        type = 'government',
        ministry = 'وزارة الداخلية',
        ranks = {
            'جندي', 'عريف', 'وكيل رقيب', 'رقيب', 'رقيب أول',
            'مساعد', 'مساعد أول', 'ملازم', 'ملازم أول', 'نقيب',
            'رائد', 'مقدم', 'عقيد', 'عميد', 'لواء'
        },
        permissions = {
            'arrest', 'fine', 'search', 'patrol', 'investigate'
        }
    },
    
    ambulance = {
        name = 'إسعاف',
        nameEN = 'Ambulance',
        salary = 2500,
        type = 'government',
        ministry = 'وزارة الصحة',
        ranks = {
            'مسعف', 'مسعف أول', 'رئيس مسعفين', 'مشرف إسعاف', 'مدير إسعاف'
        },
        permissions = {
            'heal', 'revive', 'transport', 'emergency_response'
        }
    },
    
    fire = {
        name = 'إطفاء',
        nameEN = 'Fire Department',
        salary = 2500,
        type = 'government',
        ministry = 'وزارة الداخلية',
        ranks = {
            'رجل إطفاء', 'رجل إطفاء أول', 'عريف إطفاء', 'رقيب إطفاء', 'مفتش إطفاء'
        },
        permissions = {
            'extinguish', 'rescue', 'emergency_response', 'safety_inspection'
        }
    },
    
    aramco = {
        name = 'أرامكو',
        nameEN = 'Saudi Aramco',
        salary = 5000,
        type = 'government',
        ministry = 'أرامكو السعودية',
        ranks = {
            'فني', 'فني أول', 'مهندس', 'مهندس أول', 'مدير'
        },
        permissions = {
            'oil_extraction', 'refinery_access', 'transport_fuel'
        }
    },
    
    -- Private Jobs
    taxi = {
        name = 'تاكسي',
        nameEN = 'Taxi',
        salary = 1500,
        type = 'private',
        ministry = 'وزارة النقل',
        ranks = {
            'سائق', 'سائق أول', 'مشرف', 'مدير'
        },
        permissions = {
            'transport_passengers', 'collect_fare'
        }
    }
}

-- Exports
exports {
    'GetPlayerJob',
    'SetPlayerJob',
    'GetJobInfo',
    'GetJobSalary',
    'GetJobRank',
    'SetJobRank',
    'GetJobPermissions',
    'HasJobPermission',
    'PaySalary',
    'GetOnlineJobMembers',
    'SendJobMessage',
    'CreateJobBlip',
    'RemoveJobBlip'
}
