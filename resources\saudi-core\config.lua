-- Saudi Core Configuration
-- إعدادات النظام الأساسي السعودي

Config = {}

-- Server Information
Config.ServerName = "سيرفر المملكة العربية السعودية"
Config.ServerNameEN = "Saudi Arabia Roleplay Server"
Config.Version = "1.0.0"

-- Saudi Settings
Config.Saudi = {
    Country = "المملكة العربية السعودية",
    CountryEN = "Saudi Arabia",
    Capital = "الرياض",
    CapitalEN = "Riyadh",
    Currency = "ريال سعودي",
    CurrencyEN = "Saudi Riyal",
    CurrencyCode = "SAR",
    Timezone = "Asia/Riyadh",
    Locale = "ar-SA",
    PhoneCode = "+966",
    CountryCode = "SA"
}

-- Saudi Cities
Config.Cities = {
    {name = "الرياض", nameEN = "Riyadh", coords = vector3(-1037.0, -2737.0, 20.2), population = 7000000},
    {name = "جدة", nameEN = "Jeddah", coords = vector3(1400.0, 1100.0, 114.0), population = 4000000},
    {name = "الدمام", nameEN = "Dammam", coords = vector3(-3000.0, 1000.0, 12.0), population = 1500000},
    {name = "مكة المكرمة", nameEN = "Mecca", coords = vector3(2000.0, 3000.0, 48.0), population = 2000000},
    {name = "المدينة المنورة", nameEN = "Medina", coords = vector3(-2000.0, 2500.0, 33.0), population = 1500000},
    {name = "الطائف", nameEN = "Taif", coords = vector3(1500.0, 2000.0, 1500.0), population = 1200000},
    {name = "بريدة", nameEN = "Buraidah", coords = vector3(-500.0, -1500.0, 25.0), population = 650000},
    {name = "تبوك", nameEN = "Tabuk", coords = vector3(-2500.0, 3500.0, 850.0), population = 550000},
    {name = "حائل", nameEN = "Hail", coords = vector3(-1500.0, 500.0, 1000.0), population = 400000},
    {name = "أبها", nameEN = "Abha", coords = vector3(3000.0, 1500.0, 2200.0), population = 350000},
    {name = "الخبر", nameEN = "Khobar", coords = vector3(-2800.0, 1200.0, 15.0), population = 500000},
    {name = "الظهران", nameEN = "Dhahran", coords = vector3(-2900.0, 900.0, 18.0), population = 150000},
    {name = "الجبيل", nameEN = "Jubail", coords = vector3(-3200.0, 800.0, 10.0), population = 380000},
    {name = "ينبع", nameEN = "Yanbu", coords = vector3(800.0, 2500.0, 5.0), population = 250000},
    {name = "الأحساء", nameEN = "Al-Ahsa", coords = vector3(-3500.0, 500.0, 150.0), population = 1100000}
}

-- Emergency Numbers
Config.Emergency = {
    Police = "999",
    Ambulance = "997",
    Fire = "998",
    Traffic = "993",
    Coast_Guard = "996",
    Drug_Control = "995",
    Electricity = "933",
    Water = "920001122",
    Gas = "933"
}

-- Government Ministries
Config.Ministries = {
    {name = "وزارة الداخلية", nameEN = "Ministry of Interior", phone = "999", website = "moi.gov.sa"},
    {name = "وزارة الصحة", nameEN = "Ministry of Health", phone = "997", website = "moh.gov.sa"},
    {name = "وزارة التعليم", nameEN = "Ministry of Education", phone = "19996", website = "moe.gov.sa"},
    {name = "وزارة النقل", nameEN = "Ministry of Transport", phone = "993", website = "mot.gov.sa"},
    {name = "وزارة العدل", nameEN = "Ministry of Justice", phone = "920020000", website = "moj.gov.sa"},
    {name = "وزارة المالية", nameEN = "Ministry of Finance", phone = "920001000", website = "mof.gov.sa"},
    {name = "وزارة التجارة", nameEN = "Ministry of Commerce", phone = "19993", website = "mc.gov.sa"},
    {name = "وزارة العمل", nameEN = "Ministry of Labor", phone = "19911", website = "hrsd.gov.sa"},
    {name = "وزارة الإسكان", nameEN = "Ministry of Housing", phone = "*********", website = "housing.gov.sa"},
    {name = "وزارة البيئة", nameEN = "Ministry of Environment", phone = "*********", website = "mewa.gov.sa"}
}

-- Saudi Banks
Config.Banks = {
    {name = "البنك الأهلي السعودي", nameEN = "National Commercial Bank", code = "NCB", swift = "NCBKSAJE"},
    {name = "بنك الراجحي", nameEN = "Al Rajhi Bank", code = "RAJHI", swift = "RJHISARI"},
    {name = "بنك الرياض", nameEN = "Riyad Bank", code = "RIYADH", swift = "RIBLSARI"},
    {name = "ساب", nameEN = "SABB", code = "SABB", swift = "SABBSARI"},
    {name = "البنك السعودي الفرنسي", nameEN = "Banque Saudi Fransi", code = "BSF", swift = "BSFRSARI"},
    {name = "البنك العربي الوطني", nameEN = "Arab National Bank", code = "ANB", swift = "ARNBSARI"},
    {name = "بنك الجزيرة", nameEN = "Bank AlJazira", code = "JAZIRA", swift = "BJAZSAJE"},
    {name = "بنك الإنماء", nameEN = "Alinma Bank", code = "ALINMA", swift = "INMASARI"}
}

-- Prayer Times (Default for Riyadh)
Config.PrayerTimes = {
    Fajr = "05:30",
    Sunrise = "06:45",
    Dhuhr = "12:15",
    Asr = "15:45",
    Maghrib = "18:30",
    Isha = "20:00"
}

-- Saudi Jobs
Config.Jobs = {
    {name = "شرطة", nameEN = "police", salary = 3000, type = "government", ministry = "وزارة الداخلية"},
    {name = "إسعاف", nameEN = "ambulance", salary = 2500, type = "government", ministry = "وزارة الصحة"},
    {name = "إطفاء", nameEN = "fire", salary = 2500, type = "government", ministry = "وزارة الداخلية"},
    {name = "تاكسي", nameEN = "taxi", salary = 1500, type = "private", ministry = "وزارة النقل"},
    {name = "أرامكو", nameEN = "aramco", salary = 5000, type = "government", ministry = "أرامكو السعودية"},
    {name = "سابك", nameEN = "sabic", salary = 4000, type = "government", ministry = "سابك"},
    {name = "معلم", nameEN = "teacher", salary = 3500, type = "government", ministry = "وزارة التعليم"},
    {name = "طبيب", nameEN = "doctor", salary = 6000, type = "government", ministry = "وزارة الصحة"},
    {name = "مهندس", nameEN = "engineer", salary = 4500, type = "private", ministry = "القطاع الخاص"},
    {name = "محامي", nameEN = "lawyer", salary = 4000, type = "private", ministry = "وزارة العدل"},
    {name = "قاضي", nameEN = "judge", salary = 8000, type = "government", ministry = "وزارة العدل"},
    {name = "طيار", nameEN = "pilot", salary = 7000, type = "private", ministry = "الطيران المدني"},
    {name = "مضيف طيران", nameEN = "flight_attendant", salary = 3000, type = "private", ministry = "الطيران المدني"},
    {name = "صيدلي", nameEN = "pharmacist", salary = 3500, type = "private", ministry = "وزارة الصحة"},
    {name = "مصرفي", nameEN = "banker", salary = 4000, type = "private", ministry = "البنك المركزي"}
}

-- Saudi Vehicles
Config.Vehicles = {
    -- Police Vehicles
    {model = "police", name = "سيارة شرطة", nameEN = "Police Car", type = "emergency", job = "police"},
    {model = "police2", name = "دورية شرطة", nameEN = "Police Patrol", type = "emergency", job = "police"},
    {model = "policeb", name = "دراجة شرطة", nameEN = "Police Bike", type = "emergency", job = "police"},
    
    -- Ambulance Vehicles
    {model = "ambulance", name = "سيارة إسعاف", nameEN = "Ambulance", type = "emergency", job = "ambulance"},
    
    -- Fire Vehicles
    {model = "firetruk", name = "سيارة إطفاء", nameEN = "Fire Truck", type = "emergency", job = "fire"},
    
    -- Taxi Vehicles
    {model = "taxi", name = "تاكسي", nameEN = "Taxi", type = "service", job = "taxi"},
    
    -- Civilian Vehicles
    {model = "adder", name = "أدر", nameEN = "Adder", type = "super", job = "civilian"},
    {model = "zentorno", name = "زينتورنو", nameEN = "Zentorno", type = "super", job = "civilian"},
    {model = "t20", name = "تي 20", nameEN = "T20", type = "super", job = "civilian"}
}

-- Saudi Holidays
Config.Holidays = {
    {name = "اليوم الوطني", nameEN = "National Day", date = "09-23", type = "national"},
    {name = "يوم التأسيس", nameEN = "Founding Day", date = "02-22", type = "national"},
    {name = "عيد الفطر", nameEN = "Eid Al-Fitr", date = "variable", type = "religious"},
    {name = "عيد الأضحى", nameEN = "Eid Al-Adha", date = "variable", type = "religious"},
    {name = "رأس السنة الهجرية", nameEN = "Islamic New Year", date = "variable", type = "religious"},
    {name = "المولد النبوي", nameEN = "Prophet's Birthday", date = "variable", type = "religious"}
}

-- Economy Settings
Config.Economy = {
    StartingMoney = 5000,
    MaxMoney = *********,
    Currency = "SAR",
    TaxRate = 0.15,
    ZakatRate = 0.025,
    MinimumWage = 3000,
    UnemploymentBenefit = 2000,
    SocialInsurance = 0.09,
    MedicalInsurance = 0.02
}

-- Identity Settings
Config.Identity = {
    SaudiIDFormat = "1XXXXXXXXX",
    IqamaIDFormat = "2XXXXXXXXX",
    PassportFormat = "AXXXXXXXX",
    PhoneFormat = "+966-XX-XXX-XXXX",
    IBANFormat = "SA00-0000-0000-0000-0000-0000",
    MinAge = 18,
    MaxAge = 80,
    RequiredDocuments = {"saudi_id", "passport", "driving_license"}
}

-- Business Settings
Config.Business = {
    RegistrationCost = 10000,
    LicenseCost = 5000,
    TaxRate = 0.15,
    MaxEmployees = 50,
    MinCapital = 50000,
    RequiredLicenses = {"commercial_license", "municipal_license", "zakat_certificate"}
}

-- Property Settings
Config.Property = {
    TaxRate = 0.01,
    MaxOwned = 5,
    RegistrationCost = 2000,
    TransferCost = 1000,
    InsuranceRate = 0.005,
    MaintenanceCost = 500
}

-- Education Settings
Config.Education = {
    Universities = {
        {name = "جامعة الملك سعود", nameEN = "King Saud University", city = "الرياض"},
        {name = "جامعة الملك عبدالعزيز", nameEN = "King Abdulaziz University", city = "جدة"},
        {name = "جامعة الملك فهد", nameEN = "King Fahd University", city = "الظهران"},
        {name = "جامعة الإمام", nameEN = "Imam University", city = "الرياض"},
        {name = "جامعة أم القرى", nameEN = "Umm Al-Qura University", city = "مكة المكرمة"}
    },
    Degrees = {"بكالوريوس", "ماجستير", "دكتوراه"},
    ScholarshipAmount = 2000,
    TuitionFee = 0 -- Free education in Saudi Arabia
}

-- Healthcare Settings
Config.Healthcare = {
    Hospitals = {
        {name = "مستشفى الملك فيصل التخصصي", nameEN = "King Faisal Specialist Hospital", city = "الرياض"},
        {name = "مستشفى الملك عبدالعزيز", nameEN = "King Abdulaziz Hospital", city = "جدة"},
        {name = "مستشفى الملك فهد", nameEN = "King Fahd Hospital", city = "الدمام"},
        {name = "مستشفى الحرس الوطني", nameEN = "National Guard Hospital", city = "الرياض"}
    },
    InsuranceTypes = {"حكومي", "خاص", "تأمين شركة"},
    TreatmentCost = 0, -- Free healthcare in Saudi Arabia
    InsuranceCoverage = 100
}

-- Transportation Settings
Config.Transportation = {
    DrivingLicenseCost = 1000,
    VehicleRegistrationCost = 500,
    InsuranceCost = 200,
    FuelPrice = {
        ["95"] = 2.18,
        ["91"] = 2.04,
        ["diesel"] = 0.75
    },
    SpeedLimits = {
        city = 80,
        highway = 120,
        residential = 50
    },
    TrafficFines = {
        speeding = 500,
        red_light = 1000,
        parking = 100,
        no_license = 2000,
        no_insurance = 1500
    }
}

-- Weather Settings
Config.Weather = {
    Riyadh = {summer = 45, winter = 15, humidity = 20},
    Jeddah = {summer = 38, winter = 25, humidity = 80},
    Dammam = {summer = 42, winter = 18, humidity = 70},
    Abha = {summer = 28, winter = 8, humidity = 40},
    Tabuk = {summer = 35, winter = 5, humidity = 30}
}

-- Cultural Settings
Config.Culture = {
    Languages = {"العربية", "English"},
    Religions = {"الإسلام", "المسيحية", "اليهودية", "أخرى"},
    Traditions = {"الضيافة", "الكرم", "الشجاعة", "الصدق"},
    Festivals = {"الجنادرية", "موسم الرياض", "شتاء طنطورة", "مهرجان الورد"},
    Sports = {"كرة القدم", "سباق الهجن", "الصقارة", "الفروسية"},
    Food = {"الكبسة", "المندي", "الشاورما", "التمر", "القهوة العربية"}
}

-- Security Settings
Config.Security = {
    PoliceStations = 15,
    Checkpoints = 50,
    CameraSystem = true,
    EmergencyResponse = 5, -- minutes
    CrimeRate = "منخفض",
    SafetyIndex = 95
}

-- Development Settings
Config.Development = {
    Vision2030 = true,
    NEOM = true,
    RedSea = true,
    Qiddiya = true,
    SmartCities = {"الرياض", "جدة", "الدمام"},
    GreenInitiatives = true,
    RenewableEnergy = 50 -- percentage target
}

-- Export configuration
_G.Config = Config
