-- Session Manager for Saudi FiveM Server
-- مدير الجلسات للسيرفر السعودي

print('^2[Session Manager] ^7Saudi session system loaded')
print('^2[مدير الجلسات] ^7تم تحميل نظام الجلسات السعودي')

-- Handle player connecting
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    print('^3[Session Manager] ^7Player session started: ' .. name)
    print('^3[مدير الجلسات] ^7بدأت جلسة اللاعب: ' .. name)
end)

-- Handle player dropped
AddEventHandler('playerDropped', function(reason)
    local source = source
    print('^1[Session Manager] ^7Player session ended: ' .. reason)
    print('^1[مدير الجلسات] ^7انتهت جلسة اللاعب: ' .. reason)
end)
