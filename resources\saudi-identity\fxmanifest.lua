fx_version 'cerulean'
game 'gta5'

name 'Saudi Identity System'
description 'نظام الهوية السعودية - Saudi Arabian Identity System'
author 'Saudi FiveM Development Team'
version '1.0.0'

-- Client Scripts
client_scripts {
    'client/main.lua'
}

-- Server Scripts
server_scripts {
    'server/main.lua'
}

-- No shared scripts or UI files for now

-- No dependencies needed for basic functionality

-- Saudi Identity Configuration
saudi_identity = {
    -- Document Types
    documents = {
        saudi_id = {
            name = 'الهوية الوطنية',
            nameEN = 'National ID',
            format = '1XXXXXXXXX',
            length = 10,
            required = true,
            renewable = true,
            validity_years = 10,
            cost = 0 -- Free for citizens
        },
        
        iqama = {
            name = 'الإقامة',
            nameEN = 'Iqama (Residence Permit)',
            format = '2XXXXXXXXX',
            length = 10,
            required = false,
            renewable = true,
            validity_years = 1,
            cost = 500
        },
        
        passport = {
            name = 'جواز السفر',
            nameEN = 'Passport',
            format = 'AXXXXXXXX',
            length = 9,
            required = false,
            renewable = true,
            validity_years = 10,
            cost = 300
        },
        
        driving_license = {
            name = 'رخصة القيادة',
            nameEN = 'Driving License',
            format = 'XXXXXXXXXX',
            length = 10,
            required = false,
            renewable = true,
            validity_years = 5,
            cost = 1000
        },
        
        birth_certificate = {
            name = 'شهادة الميلاد',
            nameEN = 'Birth Certificate',
            format = 'BC-XXXXXXXX',
            length = 11,
            required = true,
            renewable = false,
            validity_years = 0, -- Permanent
            cost = 0
        }
    },
    
    -- Personal Information Fields
    personal_info = {
        -- Arabic Name
        arabic_name = {
            first_name = 'الاسم الأول',
            father_name = 'اسم الأب',
            grandfather_name = 'اسم الجد',
            family_name = 'اسم العائلة'
        },
        
        -- English Name
        english_name = {
            first_name = 'First Name',
            father_name = 'Father Name',
            grandfather_name = 'Grandfather Name',
            family_name = 'Family Name'
        },
        
        -- Basic Info
        date_of_birth = 'تاريخ الميلاد',
        place_of_birth = 'مكان الميلاد',
        nationality = 'الجنسية',
        gender = 'الجنس',
        marital_status = 'الحالة الاجتماعية',
        blood_type = 'فصيلة الدم',
        
        -- Contact Info
        phone_number = 'رقم الهاتف',
        email = 'البريد الإلكتروني',
        address = 'العنوان',
        city = 'المدينة',
        postal_code = 'الرمز البريدي',
        
        -- Emergency Contact
        emergency_contact_name = 'اسم جهة الاتصال للطوارئ',
        emergency_contact_phone = 'رقم هاتف الطوارئ',
        emergency_contact_relation = 'صلة القرابة'
    },
    
    -- Saudi Cities for Birth Places
    birth_places = {
        'الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة',
        'الطائف', 'بريدة', 'تبوك', 'حائل', 'أبها', 'الخبر', 'الظهران',
        'الجبيل', 'ينبع', 'الأحساء', 'نجران', 'جازان', 'عرعر', 'سكاكا',
        'القطيف', 'الخرج', 'المجمعة', 'وادي الدواسر', 'بيشة', 'خميس مشيط'
    },
    
    -- Nationalities
    nationalities = {
        'سعودي', 'مصري', 'سوري', 'لبناني', 'أردني', 'فلسطيني', 'عراقي',
        'يمني', 'سوداني', 'مغربي', 'تونسي', 'جزائري', 'ليبي', 'باكستاني',
        'هندي', 'بنغلاديشي', 'فلبيني', 'إندونيسي', 'تركي', 'إيراني', 'أفغاني'
    },
    
    -- Blood Types
    blood_types = {
        'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
    },
    
    -- Marital Status
    marital_status = {
        'أعزب', 'متزوج', 'مطلق', 'أرمل'
    },
    
    -- Gender
    gender = {
        'ذكر', 'أنثى'
    },
    
    -- Government Offices
    government_offices = {
        {
            name = 'مكتب الأحوال المدنية - الرياض',
            nameEN = 'Civil Affairs Office - Riyadh',
            coords = vector3(-1200.0, -2800.0, 20.0),
            city = 'الرياض',
            services = {'saudi_id', 'birth_certificate', 'family_card'}
        },
        {
            name = 'مكتب الجوازات - الرياض',
            nameEN = 'Passport Office - Riyadh',
            coords = vector3(-1150.0, -2750.0, 20.0),
            city = 'الرياض',
            services = {'passport', 'iqama', 'visa'}
        },
        {
            name = 'إدارة المرور - الرياض',
            nameEN = 'Traffic Department - Riyadh',
            coords = vector3(-1100.0, -2700.0, 20.0),
            city = 'الرياض',
            services = {'driving_license', 'vehicle_registration'}
        },
        {
            name = 'مكتب الأحوال المدنية - جدة',
            nameEN = 'Civil Affairs Office - Jeddah',
            coords = vector3(1300.0, 1000.0, 114.0),
            city = 'جدة',
            services = {'saudi_id', 'birth_certificate', 'family_card'}
        },
        {
            name = 'مكتب الجوازات - جدة',
            nameEN = 'Passport Office - Jeddah',
            coords = vector3(1350.0, 1050.0, 114.0),
            city = 'جدة',
            services = {'passport', 'iqama', 'visa'}
        }
    },
    
    -- Required Documents for Services
    service_requirements = {
        saudi_id = {
            'birth_certificate',
            'family_card',
            'photo'
        },
        iqama = {
            'passport',
            'visa',
            'sponsor_letter',
            'medical_report',
            'photo'
        },
        passport = {
            'saudi_id',
            'birth_certificate',
            'photo'
        },
        driving_license = {
            'saudi_id',
            'medical_report',
            'driving_test',
            'photo'
        }
    },
    
    -- Absher Services (Digital Government Platform)
    absher_services = {
        {
            name = 'تجديد الهوية الوطنية',
            nameEN = 'Renew National ID',
            service_code = 'ABS001',
            cost = 0,
            processing_time = '3 days'
        },
        {
            name = 'استخراج شهادة ميلاد',
            nameEN = 'Issue Birth Certificate',
            service_code = 'ABS002',
            cost = 0,
            processing_time = '1 day'
        },
        {
            name = 'تجديد جواز السفر',
            nameEN = 'Renew Passport',
            service_code = 'ABS003',
            cost = 300,
            processing_time = '7 days'
        },
        {
            name = 'تجديد رخصة القيادة',
            nameEN = 'Renew Driving License',
            service_code = 'ABS004',
            cost = 40,
            processing_time = '1 day'
        },
        {
            name = 'استعلام عن المخالفات المرورية',
            nameEN = 'Traffic Violations Inquiry',
            service_code = 'ABS005',
            cost = 0,
            processing_time = 'instant'
        }
    },
    
    -- Validation Rules
    validation = {
        saudi_id = {
            pattern = '^1[0-9]{9}$',
            checksum = true
        },
        iqama = {
            pattern = '^2[0-9]{9}$',
            checksum = true
        },
        phone = {
            pattern = '^(\\+966|0)?5[0-9]{8}$'
        },
        email = {
            pattern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
        },
        postal_code = {
            pattern = '^[0-9]{5}$'
        }
    }
}

-- Exports
exports {
    'GetPlayerIdentity',
    'SetPlayerIdentity',
    'ValidateDocument',
    'GenerateDocumentNumber',
    'RegisterCitizen',
    'UpdateCitizenInfo',
    'GetDocumentInfo',
    'RenewDocument',
    'GetGovernmentServices',
    'ProcessAbsherRequest',
    'ValidateSaudiID',
    'ValidateIqamaID',
    'ValidatePhoneNumber',
    'ValidateEmail',
    'GetBirthCertificate',
    'IssueFamilyCard',
    'GetPassportInfo',
    'GetDrivingLicense',
    'CheckDocumentExpiry',
    'SendDocumentReminder'
}
