# Saudi FiveM Server - Developer Guide
# دليل المطور للسيرفر السعودي

## نظرة عامة | Overview

هذا الدليل مخصص للمطورين الذين يريدون المساهمة في تطوير السيرفر السعودي أو إنشاء موارد إضافية.

This guide is for developers who want to contribute to the Saudi server development or create additional resources.

## هيكل المشروع | Project Structure

```
SERVERFIVEM/
├── server.cfg                 # إعدادات السيرفر الرئيسية
├── database.cfg              # إعدادات قاعدة البيانات
├── resources.cfg             # قائمة الموارد
├── start.bat                 # ملف تشغيل Windows
├── start.sh                  # ملف تشغيل Linux
├── setup.bat                 # ملف الإعداد
├── README.md                 # دليل المستخدم
├── DEVELOPER_GUIDE.md        # دليل المطور
└── resources/                # مجلد الموارد
    ├── mongodb/              # مورد قاعدة البيانات
    ├── saudi-core/           # النظام الأساسي السعودي
    ├── saudi-jobs/           # نظام الوظائف
    ├── saudi-vehicles/       # نظام المركبات
    ├── saudi-economy/        # النظام الاقتصادي
    ├── saudi-identity/       # نظام الهوية
    ├── arabic-ui/            # الواجهة العربية
    └── [other resources]/    # موارد أخرى
```

## قاعدة البيانات | Database

### MongoDB Collections

```javascript
// Users Collection
{
  _id: ObjectId,
  steam_id: String,
  saudi_id: String,
  iqama_id: String,
  name: {
    arabic: {
      first: String,
      father: String,
      grandfather: String,
      family: String
    },
    english: {
      first: String,
      father: String,
      grandfather: String,
      family: String
    }
  },
  personal_info: {
    date_of_birth: Date,
    place_of_birth: String,
    nationality: String,
    gender: String,
    blood_type: String,
    phone: String,
    email: String
  },
  created_at: Date,
  updated_at: Date
}

// Characters Collection
{
  _id: ObjectId,
  user_id: ObjectId,
  name: String,
  job: String,
  job_rank: Number,
  money: {
    cash: Number,
    bank: Number
  },
  position: {
    x: Number,
    y: Number,
    z: Number
  },
  created_at: Date,
  last_login: Date
}

// Vehicles Collection
{
  _id: ObjectId,
  owner_id: ObjectId,
  model: String,
  plate: String,
  color: Object,
  modifications: Object,
  fuel: Number,
  engine_health: Number,
  body_health: Number,
  registration: {
    date: Date,
    expiry: Date,
    cost: Number
  },
  insurance: {
    active: Boolean,
    expiry: Date,
    cost: Number
  }
}
```

### Database Functions

```lua
-- MongoDB Wrapper Functions
exports.mongodb:insertOne(collection, document, callback)
exports.mongodb:findOne(collection, query, callback)
exports.mongodb:updateOne(collection, query, update, callback)
exports.mongodb:deleteOne(collection, query, callback)

-- Saudi Specific Functions
exports.mongodb:validateSaudiID(id)
exports.mongodb:validateIqamaID(id)
exports.mongodb:getPrayerTimes(city, date)
exports.mongodb:calculateZakat(amount)
```

## إنشاء مورد جديد | Creating New Resource

### 1. إنشاء المجلد | Create Folder
```bash
mkdir resources/my-saudi-resource
cd resources/my-saudi-resource
```

### 2. إنشاء fxmanifest.lua | Create fxmanifest.lua
```lua
fx_version 'cerulean'
game 'gta5'

name 'My Saudi Resource'
description 'وصف المورد السعودي'
author 'Your Name'
version '1.0.0'

client_scripts {
    'client.lua'
}

server_scripts {
    '@mongodb/lib/mongodb.lua',
    'server.lua'
}

dependencies {
    'saudi-core',
    'mongodb'
}
```

### 3. إنشاء Server Script | Create Server Script
```lua
-- server.lua
local SaudiResource = {}

-- Initialize resource
Citizen.CreateThread(function()
    print('^2[My Saudi Resource] ^7تم تحميل المورد السعودي')
    print('^2[My Saudi Resource] ^7Saudi resource loaded')
end)

-- Example function
function SaudiResource.GetPlayerSaudiData(playerId)
    local steamId = GetPlayerIdentifier(playerId, 0)
    
    exports.mongodb:findOne('users', {steam_id = steamId}, function(success, result)
        if success and result then
            return result
        else
            return nil
        end
    end)
end

-- Export functions
exports('GetPlayerSaudiData', SaudiResource.GetPlayerSaudiData)
```

### 4. إنشاء Client Script | Create Client Script
```lua
-- client.lua
local SaudiResource = {}

-- Initialize client
Citizen.CreateThread(function()
    print('^2[My Saudi Resource] ^7تم تحميل العميل السعودي')
    print('^2[My Saudi Resource] ^7Saudi client loaded')
end)

-- Example function
function SaudiResource.ShowSaudiNotification(message)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(message)
    DrawNotification(0, 1)
end

-- Export functions
exports('ShowSaudiNotification', SaudiResource.ShowSaudiNotification)
```

## معايير التطوير | Development Standards

### 1. التسمية | Naming Conventions
- استخدم أسماء وصفية باللغة الإنجليزية
- أضف تعليقات باللغة العربية والإنجليزية
- استخدم البادئة `saudi-` للموارد السعودية

### 2. التوطين | Localization
```lua
-- Example localization
local Locales = {
    ar = {
        welcome = 'أهلاً وسهلاً',
        goodbye = 'مع السلامة',
        police = 'شرطة',
        hospital = 'مستشفى'
    },
    en = {
        welcome = 'Welcome',
        goodbye = 'Goodbye',
        police = 'Police',
        hospital = 'Hospital'
    }
}

function GetLocale(key, lang)
    lang = lang or 'ar'
    return Locales[lang][key] or key
end
```

### 3. معالجة الأخطاء | Error Handling
```lua
-- Always handle errors properly
function SafeDatabaseOperation(collection, query, callback)
    exports.mongodb:findOne(collection, query, function(success, result)
        if success then
            if callback then callback(true, result) end
        else
            print('^1[ERROR] Database operation failed')
            print('^1[خطأ] فشلت عملية قاعدة البيانات')
            if callback then callback(false, nil) end
        end
    end)
end
```

### 4. الأداء | Performance
```lua
-- Use efficient loops and avoid blocking operations
Citizen.CreateThread(function()
    while true do
        -- Your code here
        Citizen.Wait(1000) -- Don't use Wait(0) unless necessary
    end
end)

-- Use events instead of continuous loops when possible
RegisterNetEvent('saudi:updatePlayerData')
AddEventHandler('saudi:updatePlayerData', function(data)
    -- Handle event
end)
```

## واجهة المستخدم | User Interface

### 1. HTML Structure
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Saudi UI</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="saudi-container">
        <h1>السيرفر السعودي</h1>
        <p>مرحباً بك في السيرفر السعودي</p>
    </div>
    <script src="script.js"></script>
</body>
</html>
```

### 2. CSS Styling
```css
/* Saudi theme colors */
:root {
    --saudi-green: #006C35;
    --saudi-white: #FFFFFF;
    --saudi-gold: #FFD700;
}

body {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
    direction: rtl;
    text-align: right;
    background-color: var(--saudi-white);
    color: #333;
}

.saudi-container {
    background: linear-gradient(135deg, var(--saudi-green), var(--saudi-gold));
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
```

### 3. JavaScript Functions
```javascript
// Saudi UI utilities
const SaudiUI = {
    // Show Arabic notification
    showNotification: function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    },
    
    // Format Saudi currency
    formatCurrency: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    },
    
    // Format Arabic date
    formatDate: function(date) {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    }
};
```

## اختبار الموارد | Testing Resources

### 1. اختبار الوحدة | Unit Testing
```lua
-- test.lua
local function TestSaudiIDValidation()
    local validID = '1234567890'
    local invalidID = '0123456789'
    
    assert(exports.mongodb:validateSaudiID(validID) == true, 'Valid Saudi ID should pass')
    assert(exports.mongodb:validateSaudiID(invalidID) == false, 'Invalid Saudi ID should fail')
    
    print('^2[TEST] ^7Saudi ID validation tests passed')
end

-- Run tests
TestSaudiIDValidation()
```

### 2. اختبار التكامل | Integration Testing
```lua
-- integration_test.lua
local function TestDatabaseConnection()
    exports.mongodb:findOne('users', {}, function(success, result)
        if success then
            print('^2[TEST] ^7Database connection successful')
        else
            print('^1[TEST] ^7Database connection failed')
        end
    end)
end

TestDatabaseConnection()
```

## نشر الموارد | Deploying Resources

### 1. التحقق من الجودة | Quality Check
- تأكد من عدم وجود أخطاء في الكود
- اختبر جميع الوظائف
- تحقق من التوطين العربي
- راجع الأداء

### 2. التوثيق | Documentation
- أضف تعليقات واضحة
- اكتب دليل الاستخدام
- وثق جميع الوظائف المُصدرة
- أضف أمثلة للاستخدام

### 3. الإصدار | Release
```bash
# Create release folder
mkdir release/my-saudi-resource

# Copy necessary files
cp -r client.lua server.lua fxmanifest.lua html/ release/my-saudi-resource/

# Create documentation
echo "# My Saudi Resource" > release/my-saudi-resource/README.md
```

## المساهمة | Contributing

### 1. Fork المشروع | Fork Project
```bash
git clone https://github.com/your-username/saudi-fivem-server.git
cd saudi-fivem-server
```

### 2. إنشاء فرع جديد | Create Branch
```bash
git checkout -b feature/my-saudi-feature
```

### 3. إضافة التحسينات | Add Improvements
- اتبع معايير التطوير
- أضف اختبارات
- وثق التغييرات

### 4. إرسال Pull Request | Submit PR
```bash
git add .
git commit -m "Add: Saudi feature description"
git push origin feature/my-saudi-feature
```

## الدعم الفني | Technical Support

### مشاكل شائعة | Common Issues

**1. خطأ في تحميل المورد**
```
[ERROR] Failed to start resource 'my-saudi-resource'
```
**الحل**: تحقق من صحة fxmanifest.lua

**2. خطأ في قاعدة البيانات**
```
[ERROR] MongoDB connection failed
```
**الحل**: تحقق من رابط الاتصال في database.cfg

**3. مشكلة في الواجهة العربية**
```
[WARNING] Arabic text not displaying correctly
```
**الحل**: تأكد من استخدام UTF-8 encoding

### أدوات التطوير | Development Tools

**1. محرر النصوص المُوصى به**
- Visual Studio Code
- Sublime Text
- Atom

**2. إضافات مفيدة**
- Lua Language Server
- FiveM Development Tools
- Arabic Language Support

**3. أدوات اختبار**
- FiveM Server Console
- Database Management Tools
- Browser Developer Tools

## الموارد المفيدة | Useful Resources

### الوثائق | Documentation
- [FiveM Documentation](https://docs.fivem.net/)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [Lua Reference](https://www.lua.org/manual/5.3/)

### المجتمع | Community
- [FiveM Forums](https://forum.cfx.re/)
- [Saudi FiveM Discord](https://discord.gg/saudifivem)
- [GitHub Repository](https://github.com/saudi-fivem/server)

### أدوات | Tools
- [FiveM Resource Generator](https://resource-generator.fivem.net/)
- [Lua Formatter](https://marketplace.visualstudio.com/items?itemName=Koihik.vscode-lua)
- [MongoDB Compass](https://www.mongodb.com/products/compass)

---

**🇸🇦 تم إنشاؤه بحب في المملكة العربية السعودية | Made with ❤️ in Saudi Arabia 🇸🇦**
