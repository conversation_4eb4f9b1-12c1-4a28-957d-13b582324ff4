# MongoDB Database Configuration for Saudi FiveM Server
# إعدادات قاعدة البيانات مونقو دي بي للسيرفر السعودي

# MongoDB Connection String
set mongodb_connection_string "mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"

# Database Name
set mongodb_database "saudi_fivem_server"

# Connection Settings
set mongodb_timeout 30000
set mongodb_retry_writes true
set mongodb_w_majority true

# Collections Configuration
set mongodb_users_collection "users"
set mongodb_characters_collection "characters"
set mongodb_vehicles_collection "vehicles"
set mongodb_properties_collection "properties"
set mongodb_jobs_collection "jobs"
set mongodb_inventory_collection "inventory"
set mongodb_banking_collection "banking"
set mongodb_licenses_collection "licenses"
set mongodb_businesses_collection "businesses"
set mongodb_logs_collection "logs"

# Saudi Specific Collections
set mongodb_saudi_ids_collection "saudi_national_ids"
set mongodb_prayer_times_collection "prayer_times"
set mongodb_hajj_permits_collection "hajj_permits"
set mongodb_umrah_permits_collection "umrah_permits"
set mongodb_driving_licenses_collection "saudi_driving_licenses"
set mongodb_iqama_collection "iqama_records"
set mongodb_saudi_jobs_collection "saudi_government_jobs"
set mongodb_aramco_employees_collection "aramco_employees"
set mongodb_saudi_businesses_collection "saudi_business_registry"
set mongodb_zakat_collection "zakat_records"

# Connection Pool Settings
set mongodb_max_pool_size 100
set mongodb_min_pool_size 5
set mongodb_max_idle_time 30000

# Security Settings
set mongodb_ssl true
set mongodb_auth_source "admin"

# Backup Settings
set mongodb_backup_enabled true
set mongodb_backup_interval 3600
set mongodb_backup_retention 7

# Logging
set mongodb_log_level "info"
set mongodb_log_queries false

# Performance Settings
set mongodb_read_preference "primary"
set mongodb_write_concern_w 1
set mongodb_write_concern_j true

# Saudi Specific Database Settings
set saudi_id_format "1XXXXXXXXX"
set iqama_id_format "2XXXXXXXXX"
set passport_format "AXXXXXXXX"

# Regional Settings
set database_timezone "Asia/Riyadh"
set database_locale "ar-SA"
set database_currency "SAR"

# Indexes for Performance
set create_user_indexes true
set create_character_indexes true
set create_vehicle_indexes true
set create_saudi_id_indexes true

# Data Validation
set validate_saudi_id true
set validate_phone_numbers true
set validate_iban true

# Encryption Settings
set encrypt_sensitive_data true
set encryption_key "YOUR_ENCRYPTION_KEY_HERE"

# Audit Trail
set audit_enabled true
set audit_collection "audit_logs"

# Data Retention Policies
set logs_retention_days 90
set backup_retention_days 30
set audit_retention_days 365

# Saudi Government Integration (Future)
set absher_integration false
set nafath_integration false
set elm_integration false

# Banking Integration
set saudi_banks_integration true
set iban_validation true
set swift_code_validation true

# Hajj and Umrah Database
set hajj_database_enabled true
set umrah_database_enabled true
set mecca_access_control true

# Prayer Times Database
set prayer_times_auto_update true
set prayer_times_source "islamic_finder"
set hijri_calendar_enabled true

# Saudi Cultural Database
set cultural_events_db true
set national_holidays_db true
set ramadan_schedule_db true

# Government Services Database
set government_services_db true
set ministry_services_db true
set municipal_services_db true

# Healthcare Database
set healthcare_records_db true
set vaccination_records_db true
set health_insurance_db true

# Education Database
set education_records_db true
set university_records_db true
set scholarship_records_db true

# Transportation Database
set driving_records_db true
set traffic_violations_db true
set vehicle_registration_db true

# Business Database
set business_registry_db true
set commercial_license_db true
set tax_records_db true

# Real Estate Database
set property_registry_db true
set real_estate_transactions_db true
set property_valuation_db true

# Employment Database
set employment_records_db true
set salary_records_db true
set social_insurance_db true

# Zakat and Charity Database
set zakat_calculation_db true
set charity_records_db true
set donation_tracking_db true

# Sports and Recreation Database
set sports_clubs_db true
set recreation_facilities_db true
set tournament_records_db true

# Tourism Database
set tourist_attractions_db true
set hotel_bookings_db true
set tour_packages_db true

# Weather Database
set weather_data_db true
set climate_records_db true
set seasonal_data_db true

# Emergency Services Database
set emergency_contacts_db true
set incident_reports_db true
set response_times_db true

# Communication Database
set phone_directory_db true
set emergency_numbers_db true
set government_contacts_db true

# Legal Database
set legal_documents_db true
set court_records_db true
set legal_procedures_db true

# Agricultural Database
set agricultural_data_db true
set crop_records_db true
set livestock_records_db true

# Energy Database
set energy_consumption_db true
set renewable_energy_db true
set oil_gas_records_db true

# Water Resources Database
set water_usage_db true
set desalination_data_db true
set water_quality_db true

# Telecommunications Database
set telecom_services_db true
set internet_providers_db true
set mobile_operators_db true

# Postal Services Database
set postal_codes_db true
set mail_services_db true
set courier_services_db true

# Customs and Trade Database
set customs_records_db true
set import_export_db true
set trade_statistics_db true

# Aviation Database
set flight_records_db true
set airport_services_db true
set aviation_safety_db true

# Maritime Database
set port_records_db true
set shipping_data_db true
set maritime_safety_db true

# Mining Database
set mining_permits_db true
set mineral_resources_db true
set mining_safety_db true

# Manufacturing Database
set manufacturing_data_db true
set industrial_permits_db true
set quality_control_db true

# Retail Database
set retail_licenses_db true
set consumer_protection_db true
set market_data_db true

# Financial Services Database
set banking_services_db true
set insurance_records_db true
set investment_data_db true

# Technology Database
set tech_companies_db true
set innovation_records_db true
set digital_services_db true

# Environmental Database
set environmental_data_db true
set pollution_monitoring_db true
set conservation_records_db true

# Social Services Database
set social_programs_db true
set welfare_records_db true
set community_services_db true

# Cultural Heritage Database
set heritage_sites_db true
set cultural_artifacts_db true
set traditional_crafts_db true

# Media Database
set media_licenses_db true
set broadcasting_records_db true
set publication_data_db true

# Research Database
set research_projects_db true
set academic_publications_db true
set innovation_patents_db true

# Quality Assurance Database
set quality_standards_db true
set certification_records_db true
set compliance_data_db true

# Statistics Database
set population_statistics_db true
set economic_indicators_db true
set social_statistics_db true

# International Relations Database
set diplomatic_records_db true
set international_agreements_db true
set foreign_trade_db true

# Defense Database (Limited Access)
set defense_records_db false
set security_clearance_db false
set classified_data_db false

# End of Database Configuration
