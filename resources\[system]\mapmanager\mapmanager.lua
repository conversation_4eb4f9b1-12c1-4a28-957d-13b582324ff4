-- Map Manager for Saudi FiveM Server
-- مدير الخرائط للسيرفر السعودي

local currentGameType = 'freeroam'
local currentMap = 'saudi-arabia'

-- Initialize map manager
print('^2[Map Manager] ^7Saudi Arabia map loaded')
print('^2[مدير الخرائط] ^7تم تحميل خريطة المملكة العربية السعودية')

-- Export functions
function getCurrentGameType()
    return currentGameType
end

function getCurrentMap()
    return currentMap
end

function changeGameType(gameType)
    currentGameType = gameType
    print('^3[Map Manager] ^7Game type changed to: ' .. gameType)
end

function changeMap(map)
    currentMap = map
    print('^3[Map Manager] ^7Map changed to: ' .. map)
end

function doesMapSupportGameType(map, gameType)
    return true -- Saudi server supports all game types
end

function getMaps()
    return {'saudi-arabia'}
end

function roundEnded()
    print('^3[Map Manager] ^7Round ended')
end

-- Saudi specific locations
local saudiLocations = {
    riyadh = {x = -1037.0, y = -2737.0, z = 20.2},
    jeddah = {x = 1400.0, y = 1100.0, z = 114.0},
    dammam = {x = -3000.0, y = 1000.0, z = 12.0},
    mecca = {x = 2000.0, y = 3000.0, z = 48.0},
    medina = {x = -2000.0, y = 2500.0, z = 33.0}
}

-- Set Saudi Arabia as default spawn
AddEventHandler('playerConnecting', function()
    print('^2[Map Manager] ^7Player connecting to Saudi Arabia')
end)
