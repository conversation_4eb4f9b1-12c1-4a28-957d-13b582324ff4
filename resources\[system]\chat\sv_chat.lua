-- Chat Server for Saudi FiveM Server
-- خادم الدردشة للسيرفر السعودي

print('^2[Chat] ^7Saudi Arabic chat system loaded')
print('^2[الدردشة] ^7تم تحميل نظام الدردشة العربية السعودية')

-- Chat commands
RegisterCommand('say', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    if message and message ~= '' then
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 255, 255},
            multiline = true,
            args = {GetPlayerName(source), message}
        })
    end
end, false)

RegisterCommand('me', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    if message and message ~= '' then
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 194, 14},
            multiline = true,
            args = {'* ' .. GetPlayerName(source) .. ' ' .. message}
        })
    end
end, false)

RegisterCommand('ooc', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    if message and message ~= '' then
        TriggerClientEvent('chat:addMessage', -1, {
            color = {128, 128, 128},
            multiline = true,
            args = {'[OOC] ' .. GetPlayerName(source), message}
        })
    end
end, false)

-- Saudi specific commands
RegisterCommand('arabic', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    if message and message ~= '' then
        TriggerClientEvent('chat:addMessage', -1, {
            color = {0, 108, 53}, -- Saudi green
            multiline = true,
            args = {'[عربي] ' .. GetPlayerName(source), message}
        })
    end
end, false)

RegisterCommand('prayer', function(source, args, rawCommand)
    TriggerClientEvent('chat:addMessage', -1, {
        color = {0, 108, 53},
        multiline = true,
        args = {'[أذان] حان وقت الصلاة - It\'s prayer time'}
    })
end, false)

-- Handle chat message
RegisterServerEvent('chat:server:sendMessage')
AddEventHandler('chat:server:sendMessage', function(message)
    local source = source
    if message and message ~= '' then
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 255, 255},
            multiline = true,
            args = {GetPlayerName(source), message}
        })
    end
end)

-- Welcome message for new players
AddEventHandler('playerJoining', function()
    local source = source
    Citizen.Wait(5000) -- Wait 5 seconds
    
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 108, 53},
        multiline = true,
        args = {'[السيرفر السعودي]', 'أهلاً وسهلاً بك في سيرفر المملكة العربية السعودية'}
    })
    
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 108, 53},
        multiline = true,
        args = {'[Saudi Server]', 'Welcome to Saudi Arabia Roleplay Server'}
    })
end)
