fx_version 'cerulean'
game 'gta5'

name 'MongoDB Wrapper for Saudi FiveM Server'
description 'MongoDB database wrapper with Saudi localization'
author 'Saudi FiveM Development Team'
version '2.0.0'

-- MongoDB Wrapper Resource
server_scripts {
    'lib/mongodb.lua',
    'server.lua'
}

-- No dependencies needed for basic functionality

-- Saudi specific configurations
saudi_config = {
    timezone = 'Asia/Riyadh',
    locale = 'ar-SA',
    currency = 'SAR',
    country_code = 'SA'
}

-- Database collections for Saudi server
collections = {
    'users',
    'characters',
    'vehicles',
    'properties',
    'jobs',
    'inventory',
    'banking',
    'licenses',
    'businesses',
    'logs',
    'saudi_national_ids',
    'prayer_times',
    'hajj_permits',
    'umrah_permits',
    'saudi_driving_licenses',
    'iqama_records',
    'saudi_government_jobs',
    'aramco_employees',
    'saudi_business_registry',
    'zakat_records'
}

-- Export functions
exports {
    'isConnected',
    'insertOne',
    'insertMany',
    'findOne',
    'findMany',
    'updateOne',
    'updateMany',
    'deleteOne',
    'deleteMany',
    'count',
    'aggregate',
    'createIndex',
    'dropIndex',
    'createCollection',
    'dropCollection',
    'backup',
    'restore'
}

-- Saudi specific exports
saudi_exports = {
    'validateSaudiID',
    'validateIqamaID',
    'validatePhoneNumber',
    'validateIBAN',
    'getPrayerTimes',
    'getHijriDate',
    'checkHajjPermit',
    'checkUmrahPermit',
    'calculateZakat',
    'getGovernmentServices',
    'validateDrivingLicense',
    'getAramcoEmployee',
    'registerBusiness',
    'getBusinessLicense',
    'calculateTax',
    'getHealthInsurance',
    'getEducationRecord',
    'getSocialInsurance',
    'getPropertyRegistry',
    'getEmergencyContacts'
}
