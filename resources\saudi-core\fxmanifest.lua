fx_version 'cerulean'
game 'gta5'

name 'Saudi Core System'
description 'النظام الأساسي السعودي - Saudi Arabia Core System for FiveM'
author 'Saudi FiveM Development Team'
version '1.0.0'

-- Client Scripts
client_scripts {
    'config.lua',
    'client/main.lua',
    'client/ui.lua',
    'client/prayer.lua',
    'client/identity.lua',
    'client/banking.lua',
    'client/government.lua'
}

-- Server Scripts
server_scripts {
    '@mongodb/lib/mongodb.lua',
    'config.lua',
    'server/main.lua',
    'server/identity.lua',
    'server/banking.lua',
    'server/government.lua',
    'server/prayer.lua',
    'server/economy.lua'
}

-- Shared Scripts
shared_scripts {
    'shared/utils.lua',
    'shared/locales.lua'
}

-- UI Files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/style.css',
    'html/script.js',
    'html/assets/*'
}

-- Dependencies
dependencies {
    'mongodb'
}

-- Saudi Configuration
saudi_info = {
    country = 'Saudi Arabia',
    country_ar = 'المملكة العربية السعودية',
    capital = 'Riyadh',
    capital_ar = 'الرياض',
    currency = 'SAR',
    currency_ar = 'ريال سعودي',
    timezone = 'Asia/Riyadh',
    locale = 'ar-SA',
    phone_code = '+966',
    emergency = {
        police = '999',
        ambulance = '997',
        fire = '998',
        traffic = '993'
    }
}

-- Exports
exports {
    'GetPlayerSaudiID',
    'GetPlayerIqamaID',
    'ValidateIdentity',
    'GetPrayerTimes',
    'GetBankBalance',
    'TransferMoney',
    'GetGovernmentServices',
    'RegisterCitizen',
    'GetCitizenInfo',
    'UpdateCitizenInfo',
    'GetEmergencyContacts',
    'CalculateZakat',
    'GetHijriDate',
    'ConvertToHijri',
    'GetSaudiHolidays',
    'CheckBusinessLicense',
    'RegisterBusiness',
    'GetTaxInfo',
    'PayTaxes',
    'GetHealthInsurance',
    'GetEducationRecord',
    'GetDrivingLicense',
    'RenewDrivingLicense',
    'GetVehicleRegistration',
    'RegisterVehicle',
    'GetPropertyInfo',
    'RegisterProperty',
    'GetJobInfo',
    'ApplyForJob',
    'GetSalaryInfo',
    'GetSocialInsurance'
}
