-- Spawn Manager Server for Saudi FiveM Server
-- مدير الإحياء للخادم - السيرفر السعودي

print('^2[Spawn Manager Server] ^7Saudi spawn system initialized')
print('^2[مدير الإحياء - الخادم] ^7تم تهيئة نظام الإحياء السعودي')

-- Handle player connecting
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    print('^3[Spawn Manager] ^7Player ' .. name .. ' connecting to Saudi server')
    print('^3[مدير الإحياء] ^7اللاعب ' .. name .. ' يتصل بالسيرفر السعودي')
end)

-- Handle player joined
RegisterServerEvent('playerJoining')
AddEventHandler('playerJoining', function()
    local source = source
    print('^2[Spawn Manager] ^7Player joined Saudi server')
    print('^2[مدير الإحياء] ^7انضم اللاعب للسيرفر السعودي')
end)

-- Handle player dropped
AddEventHandler('playerDropped', function(reason)
    local source = source
    print('^1[Spawn Manager] ^7Player left Saudi server: ' .. reason)
    print('^1[مدير الإحياء] ^7غادر اللاعب السيرفر السعودي: ' .. reason)
end)
