@echo off
title Saudi FiveM Server Setup - إعداد السيرفر السعودي
color 0B

echo.
echo ========================================
echo    Saudi FiveM Server Setup
echo    إعداد السيرفر السعودي
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running as administrator
    echo [معلومات] يعمل بصلاحيات المدير
) else (
    echo [WARNING] Not running as administrator
    echo [تحذير] لا يعمل بصلاحيات المدير
    echo Some features may not work properly
    echo قد لا تعمل بعض الميزات بشكل صحيح
)

echo.

REM Create necessary directories
echo [INFO] Creating directories...
echo [معلومات] إنشاء المجلدات...

if not exist "cache" mkdir cache
if not exist "logs" mkdir logs
if not exist "resources\[local]" mkdir resources\[local]
if not exist "resources\[system]" mkdir resources\[system]

echo [SUCCESS] Directories created
echo [نجح] تم إنشاء المجلدات

echo.

REM Check for FiveM Server files
echo [INFO] Checking for FiveM Server files...
echo [معلومات] فحص ملفات سيرفر فايف ام...

if not exist "FXServer.exe" (
    echo [ERROR] FXServer.exe not found!
    echo [خطأ] لم يتم العثور على FXServer.exe!
    echo.
    echo Please download FiveM server files from:
    echo يرجى تحميل ملفات سيرفر فايف ام من:
    echo https://runtime.fivem.net/artifacts/fivem/build_server_windows/master/
    echo.
    echo Download the latest server build and extract all files to this directory
    echo قم بتحميل أحدث إصدار من السيرفر وفك ضغط جميع الملفات في هذا المجلد
    echo.
    pause
    exit /b 1
) else (
    echo [SUCCESS] FXServer.exe found
    echo [نجح] تم العثور على FXServer.exe
)

echo.

REM Setup configuration
echo [INFO] Setting up configuration...
echo [معلومات] إعداد التكوين...

REM Prompt for license key
echo Please enter your FiveM license key:
echo يرجى إدخال مفتاح ترخيص FiveM:
set /p LICENSE_KEY=

if "%LICENSE_KEY%"=="" (
    echo [WARNING] No license key provided
    echo [تحذير] لم يتم توفير مفتاح الترخيص
) else (
    echo [INFO] Updating license key in server.cfg...
    echo [معلومات] تحديث مفتاح الترخيص في server.cfg...
    powershell -Command "(gc server.cfg) -replace 'YOUR_LICENSE_KEY_HERE', '%LICENSE_KEY%' | Out-File -encoding ASCII server.cfg"
    echo [SUCCESS] License key updated
    echo [نجح] تم تحديث مفتاح الترخيص
)

echo.

REM Prompt for Steam Web API key
echo Please enter your Steam Web API key (optional):
echo يرجى إدخال مفتاح Steam Web API (اختياري):
set /p STEAM_KEY=

if not "%STEAM_KEY%"=="" (
    echo [INFO] Updating Steam API key in server.cfg...
    echo [معلومات] تحديث مفتاح Steam API في server.cfg...
    powershell -Command "(gc server.cfg) -replace 'YOUR_STEAM_API_KEY_HERE', '%STEAM_KEY%' | Out-File -encoding ASCII server.cfg"
    echo [SUCCESS] Steam API key updated
    echo [نجح] تم تحديث مفتاح Steam API
)

echo.

REM Prompt for RCON password
echo Please enter RCON password:
echo يرجى إدخال كلمة مرور RCON:
set /p RCON_PASS=

if "%RCON_PASS%"=="" (
    set RCON_PASS=saudi123
    echo [INFO] Using default RCON password: saudi123
    echo [معلومات] استخدام كلمة مرور RCON الافتراضية: saudi123
)

powershell -Command "(gc server.cfg) -replace 'YOUR_RCON_PASSWORD_HERE', '%RCON_PASS%' | Out-File -encoding ASCII server.cfg"
echo [SUCCESS] RCON password updated
echo [نجح] تم تحديث كلمة مرور RCON

echo.

REM Setup MongoDB connection
echo [INFO] MongoDB connection is already configured
echo [معلومات] تم تكوين اتصال MongoDB مسبقاً
echo Connection: mongodb+srv://whm:<EMAIL>/
echo الاتصال: mongodb+srv://whm:<EMAIL>/

echo.

REM Create server icon
echo [INFO] Creating server icon...
echo [معلومات] إنشاء أيقونة السيرفر...

if not exist "logo.png" (
    echo [INFO] Default server icon will be used
    echo [معلومات] سيتم استخدام الأيقونة الافتراضية
)

echo.

REM Setup firewall rules (if running as admin)
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Setting up firewall rules...
    echo [معلومات] إعداد قواعد الجدار الناري...
    
    netsh advfirewall firewall add rule name="FiveM Server TCP" dir=in action=allow protocol=TCP localport=30120 >nul 2>&1
    netsh advfirewall firewall add rule name="FiveM Server UDP" dir=in action=allow protocol=UDP localport=30120 >nul 2>&1
    
    echo [SUCCESS] Firewall rules added
    echo [نجح] تم إضافة قواعد الجدار الناري
) else (
    echo [WARNING] Cannot setup firewall rules without administrator privileges
    echo [تحذير] لا يمكن إعداد قواعد الجدار الناري بدون صلاحيات المدير
    echo Please manually allow port 30120 (TCP/UDP) in Windows Firewall
    echo يرجى السماح يدوياً للمنفذ 30120 (TCP/UDP) في جدار Windows الناري
)

echo.

REM Create batch files for easy management
echo [INFO] Creating management scripts...
echo [معلومات] إنشاء سكريبتات الإدارة...

REM Create restart script
echo @echo off > restart.bat
echo echo Restarting Saudi FiveM Server... >> restart.bat
echo echo إعادة تشغيل السيرفر السعودي... >> restart.bat
echo taskkill /f /im FXServer.exe ^>nul 2^>^&1 >> restart.bat
echo timeout /t 3 /nobreak ^>nul >> restart.bat
echo start.bat >> restart.bat

REM Create stop script
echo @echo off > stop.bat
echo echo Stopping Saudi FiveM Server... >> stop.bat
echo echo إيقاف السيرفر السعودي... >> stop.bat
echo taskkill /f /im FXServer.exe >> stop.bat
echo echo Server stopped / تم إيقاف السيرفر >> stop.bat
echo pause >> stop.bat

echo [SUCCESS] Management scripts created
echo [نجح] تم إنشاء سكريبتات الإدارة

echo.

REM Final setup summary
echo ========================================
echo    Setup Complete / اكتمل الإعداد
echo ========================================
echo.
echo Server Name: Saudi Arabia Roleplay Server
echo اسم السيرفر: سيرفر المملكة العربية السعودية
echo.
echo Database: MongoDB Atlas (Configured)
echo قاعدة البيانات: MongoDB Atlas (مُكوّن)
echo.
echo Port: 30120
echo المنفذ: 30120
echo.
echo Management Scripts:
echo سكريبتات الإدارة:
echo - start.bat (Start server / تشغيل السيرفر)
echo - restart.bat (Restart server / إعادة تشغيل السيرفر)
echo - stop.bat (Stop server / إيقاف السيرفر)
echo.
echo Features Included:
echo الميزات المتضمنة:
echo ✓ Saudi localization / التوطين السعودي
echo ✓ Arabic UI / واجهة عربية
echo ✓ Prayer times / أوقات الصلاة
echo ✓ Saudi jobs system / نظام الوظائف السعودية
echo ✓ Saudi vehicles / المركبات السعودية
echo ✓ Banking system / النظام المصرفي
echo ✓ Government services / الخدمات الحكومية
echo ✓ Identity system / نظام الهوية
echo ✓ Economy system / النظام الاقتصادي
echo.
echo To start the server, run: start.bat
echo لتشغيل السيرفر، قم بتشغيل: start.bat
echo.
echo For support, visit: https://discord.gg/saudifivem
echo للدعم الفني، زر: https://discord.gg/saudifivem
echo.

REM Ask if user wants to start server now
echo Do you want to start the server now? (y/n)
echo هل تريد تشغيل السيرفر الآن؟ (y/n)
set /p START_NOW=

if /i "%START_NOW%"=="y" (
    echo.
    echo Starting server...
    echo تشغيل السيرفر...
    start.bat
) else (
    echo.
    echo Setup complete. Run start.bat when ready.
    echo اكتمل الإعداد. قم بتشغيل start.bat عندما تكون جاهزاً.
    pause
)

exit /b 0
