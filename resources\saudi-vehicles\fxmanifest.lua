fx_version 'cerulean'
game 'gta5'

name 'Saudi Vehicles System'
description 'نظام المركبات السعودية - Saudi Arabian Vehicles System'
author 'Saudi FiveM Development Team'
version '1.0.0'

-- Client Scripts
client_scripts {
    'client/main.lua'
}

-- Server Scripts
server_scripts {
    'server/main.lua'
}

-- Shared Scripts
shared_scripts {
    'shared/vehicles.lua'
}

-- No dependencies needed for basic functionality

-- Saudi Vehicle Categories
saudi_vehicles = {
    -- Emergency Vehicles
    emergency = {
        {model = 'police', name = 'سيارة شرطة', nameEN = 'Police Car', job = 'police', price = 0},
        {model = 'police2', name = 'دورية شرطة', nameEN = 'Police Patrol', job = 'police', price = 0},
        {model = 'policeb', name = 'دراجة شرطة', nameEN = 'Police Bike', job = 'police', price = 0},
        {model = 'ambulance', name = 'سيارة إسعاف', nameEN = 'Ambulance', job = 'ambulance', price = 0},
        {model = 'firetruk', name = 'سيارة إطفاء', nameEN = 'Fire Truck', job = 'fire', price = 0}
    },
    
    -- Service Vehicles
    service = {
        {model = 'taxi', name = 'تاكسي', nameEN = 'Taxi', job = 'taxi', price = 50000},
        {model = 'bus', name = 'حافلة', nameEN = 'Bus', job = 'bus', price = 150000},
        {model = 'coach', name = 'حافلة سياحية', nameEN = 'Tour Bus', job = 'tourism', price = 200000}
    },
    
    -- Civilian Vehicles - Sedans
    sedans = {
        {model = 'sultan', name = 'سلطان', nameEN = 'Sultan', price = 80000},
        {model = 'primo', name = 'بريمو', nameEN = 'Primo', price = 65000},
        {model = 'fugitive', name = 'فوجيتيف', nameEN = 'Fugitive', price = 75000},
        {model = 'intruder', name = 'إنترودر', nameEN = 'Intruder', price = 70000},
        {model = 'stanier', name = 'ستانير', nameEN = 'Stanier', price = 60000}
    },
    
    -- SUVs
    suvs = {
        {model = 'baller', name = 'بالر', nameEN = 'Baller', price = 120000},
        {model = 'cavalcade', name = 'كافالكيد', nameEN = 'Cavalcade', price = 110000},
        {model = 'granger', name = 'جرانجر', nameEN = 'Granger', price = 95000},
        {model = 'landstalker', name = 'لاندستوكر', nameEN = 'Landstalker', price = 85000},
        {model = 'patriot', name = 'باتريوت', nameEN = 'Patriot', price = 100000}
    },
    
    -- Sports Cars
    sports = {
        {model = 'elegy2', name = 'إليجي', nameEN = 'Elegy', price = 200000},
        {model = 'jester', name = 'جيستر', nameEN = 'Jester', price = 250000},
        {model = 'massacro', name = 'ماساكرو', nameEN = 'Massacro', price = 300000},
        {model = 'rapid', name = 'رابيد', nameEN = 'Rapid GT', price = 220000}
    },
    
    -- Super Cars
    super = {
        {model = 'adder', name = 'أدر', nameEN = 'Adder', price = 1000000},
        {model = 'zentorno', name = 'زينتورنو', nameEN = 'Zentorno', price = 900000},
        {model = 't20', name = 'تي 20', nameEN = 'T20', price = 1200000},
        {model = 'osiris', name = 'أوزيريس', nameEN = 'Osiris', price = 1100000}
    },
    
    -- Motorcycles
    motorcycles = {
        {model = 'akuma', name = 'أكوما', nameEN = 'Akuma', price = 45000},
        {model = 'bati', name = 'باتي', nameEN = 'Bati 801', price = 50000},
        {model = 'double', name = 'دبل تي', nameEN = 'Double T', price = 55000},
        {model = 'pcj', name = 'بي سي جي', nameEN = 'PCJ 600', price = 40000}
    },
    
    -- Trucks
    trucks = {
        {model = 'phantom', name = 'فانتوم', nameEN = 'Phantom', price = 180000},
        {model = 'hauler', name = 'هولر', nameEN = 'Hauler', price = 160000},
        {model = 'packer', name = 'باكر', nameEN = 'Packer', price = 200000}
    },
    
    -- Vans
    vans = {
        {model = 'burrito', name = 'بوريتو', nameEN = 'Burrito', price = 70000},
        {model = 'minivan', name = 'ميني فان', nameEN = 'Minivan', price = 65000},
        {model = 'rumpo', name = 'رومبو', nameEN = 'Rumpo', price = 60000}
    }
}

-- Saudi License Plates
saudi_plates = {
    'ا ب ج ١٢٣',
    'د هـ و ٤٥٦',
    'ز ح ط ٧٨٩',
    'ي ك ل ٠١٢',
    'م ن س ٣٤٥',
    'ع ف ص ٦٧٨',
    'ق ر ش ٩٠١',
    'ت ث خ ٢٣٤'
}

-- Fuel Stations (Saudi Aramco)
fuel_stations = {
    {name = 'محطة أرامكو الرياض', coords = vector3(-1000.0, -2700.0, 20.0), city = 'الرياض'},
    {name = 'محطة أرامكو جدة', coords = vector3(1350.0, 1050.0, 114.0), city = 'جدة'},
    {name = 'محطة أرامكو الدمام', coords = vector3(-2950.0, 950.0, 12.0), city = 'الدمام'},
    {name = 'محطة أرامكو مكة', coords = vector3(1950.0, 2950.0, 48.0), city = 'مكة المكرمة'},
    {name = 'محطة أرامكو المدينة', coords = vector3(-1950.0, 2450.0, 33.0), city = 'المدينة المنورة'}
}

-- Vehicle Dealerships
dealerships = {
    {
        name = 'معرض السيارات الرياض',
        nameEN = 'Riyadh Car Dealership',
        coords = vector3(-1100.0, -2800.0, 20.0),
        city = 'الرياض',
        vehicles = {'sedans', 'suvs', 'sports'}
    },
    {
        name = 'معرض السيارات جدة',
        nameEN = 'Jeddah Car Dealership',
        coords = vector3(1300.0, 1000.0, 114.0),
        city = 'جدة',
        vehicles = {'sedans', 'suvs', 'super'}
    },
    {
        name = 'معرض الدراجات النارية',
        nameEN = 'Motorcycle Dealership',
        coords = vector3(-1050.0, -2750.0, 20.0),
        city = 'الرياض',
        vehicles = {'motorcycles'}
    }
}

-- Garages
garages = {
    {
        name = 'جراج الرياض المركزي',
        nameEN = 'Central Riyadh Garage',
        coords = vector3(-1200.0, -2900.0, 20.0),
        city = 'الرياض',
        capacity = 50
    },
    {
        name = 'جراج جدة',
        nameEN = 'Jeddah Garage',
        coords = vector3(1250.0, 950.0, 114.0),
        city = 'جدة',
        capacity = 40
    },
    {
        name = 'جراج الدمام',
        nameEN = 'Dammam Garage',
        coords = vector3(-2850.0, 850.0, 12.0),
        city = 'الدمام',
        capacity = 30
    }
}

-- Exports
exports {
    'GetVehicleInfo',
    'GetVehiclePrice',
    'BuyVehicle',
    'SellVehicle',
    'RegisterVehicle',
    'GetVehicleRegistration',
    'InsureVehicle',
    'GetVehicleInsurance',
    'RefuelVehicle',
    'GetFuelLevel',
    'GetNearestGarage',
    'GetNearestDealership',
    'GetNearestFuelStation',
    'GenerateSaudiPlate',
    'ValidatePlate'
}
