@echo off
title Saudi FiveM Server - سيرفر فايف ام السعودي
color 0A

echo.
echo ========================================
echo    Saudi FiveM Server Launcher
echo    سيرفر فايف ام السعودي
echo ========================================
echo.

REM Check if FXServer exists
if not exist "server\FXServer.exe" (
    echo [ERROR] FXServer.exe not found!
    echo [خطأ] لم يتم العثور على FXServer.exe!
    echo.
    echo Please download FiveM server files from:
    echo يرجى تحميل ملفات سيرفر فايف ام من:
    echo https://runtime.fivem.net/artifacts/fivem/build_server_windows/master/
    echo.
    pause
    exit /b 1
)

REM Check if server.cfg exists
if not exist "server.cfg" (
    echo [ERROR] server.cfg not found!
    echo [خطأ] لم يتم العثور على server.cfg!
    echo.
    pause
    exit /b 1
)

REM Display server information
echo [INFO] Starting Saudi FiveM Server...
echo [معلومات] بدء تشغيل السيرفر السعودي...
echo.
echo Server Name: Saudi Arabia Roleplay Server
echo اسم السيرفر: سيرفر المملكة العربية السعودية
echo.
echo Database: MongoDB Atlas
echo قاعدة البيانات: مونقو دي بي أطلس
echo.
echo Features:
echo المميزات:
echo - Saudi localization / التوطين السعودي
echo - Arabic UI / واجهة عربية
echo - Prayer times / أوقات الصلاة
echo - Saudi jobs / وظائف سعودية
echo - Saudi vehicles / مركبات سعودية
echo - Saudi banking / نظام مصرفي سعودي
echo - Government services / خدمات حكومية
echo - Saudi identity system / نظام الهوية السعودية
echo.

REM Set environment variables
set FIVEM_USE_SYSTEM_RESOURCES=1
set FIVEM_SKIP_RESOURCE_SCANNER=1

REM Start the server
echo [INFO] Launching FiveM Server...
echo [معلومات] تشغيل سيرفر فايف ام...
echo.

server\FXServer.exe +exec server.cfg

REM If server stops, show message
echo.
echo ========================================
echo Server has stopped / توقف السيرفر
echo ========================================
echo.
echo Press any key to restart...
echo اضغط أي زر لإعادة التشغيل...
pause > nul

REM Restart the server
goto :start

:start
cls
goto :eof
