-- Saudi Jobs Server Main
-- خادم الوظائف السعودية

print('^2[Saudi Jobs] ^7Saudi jobs system loading...')
print('^2[الوظائف السعودية] ^7جاري تحميل نظام الوظائف السعودية...')

-- Initialize Saudi Jobs
Citizen.CreateThread(function()
    Citizen.Wait(1000)
    
    print('^2[Saudi Jobs] ^7Saudi jobs system loaded!')
    print('^2[الوظائف السعودية] ^7تم تحميل نظام الوظائف السعودية!')
end)

-- Job commands
RegisterCommand('job', function(source, args, rawCommand)
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 108, 53},
        args = {'[الوظائف]', 'الوظائف المتاحة: شرطة، إسعاف، إطفاء، تاكسي، أرامكو'}
    })
end, false)
