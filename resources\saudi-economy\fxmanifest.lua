fx_version 'cerulean'
game 'gta5'

name 'Saudi Economy System'
description 'النظام الاقتصادي السعودي - Saudi Arabian Economy System'
author 'Saudi FiveM Development Team'
version '1.0.0'

-- Client Scripts
client_scripts {
    'client/main.lua'
}

-- Server Scripts
server_scripts {
    'server/main.lua'
}

-- Shared Scripts
shared_scripts {
    'shared/utils.lua'
}

-- UI Files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/style.css',
    'html/script.js',
    'html/banking/*',
    'html/business/*'
}

-- No dependencies needed for basic functionality

-- Saudi Economy Configuration
saudi_economy = {
    -- Currency
    currency = {
        name = 'ريال سعودي',
        nameEN = 'Saudi Riyal',
        code = 'SAR',
        symbol = 'ر.س',
        symbolEN = 'SR'
    },
    
    -- Starting Money
    starting_money = {
        cash = 5000,
        bank = 10000
    },
    
    -- Salary System
    salaries = {
        police = 3000,
        ambulance = 2500,
        fire = 2500,
        taxi = 1500,
        aramco = 5000,
        sabic = 4000,
        teacher = 3500,
        doctor = 6000,
        engineer = 4500,
        lawyer = 4000,
        judge = 8000,
        pilot = 7000,
        banker = 4000,
        unemployed = 2000 -- Social support
    },
    
    -- Tax System
    taxes = {
        income_tax = 0.0,      -- No income tax in Saudi Arabia
        vat = 0.15,            -- 15% VAT
        business_tax = 0.20,   -- 20% corporate tax
        zakat = 0.025,         -- 2.5% Zakat
        social_insurance = 0.09 -- 9% social insurance
    },
    
    -- Banking System
    banks = {
        {
            name = 'البنك الأهلي السعودي',
            nameEN = 'National Commercial Bank',
            code = 'NCB',
            interest_rate = 0.02,
            locations = {
                {coords = vector3(-1100.0, -2750.0, 20.0), city = 'الرياض'},
                {coords = vector3(1350.0, 1050.0, 114.0), city = 'جدة'},
                {coords = vector3(-2950.0, 950.0, 12.0), city = 'الدمام'}
            }
        },
        {
            name = 'بنك الراجحي',
            nameEN = 'Al Rajhi Bank',
            code = 'RAJHI',
            interest_rate = 0.0, -- Islamic banking (no interest)
            locations = {
                {coords = vector3(-1050.0, -2700.0, 20.0), city = 'الرياض'},
                {coords = vector3(1400.0, 1100.0, 114.0), city = 'جدة'}
            }
        }
    },
    
    -- ATM Locations
    atms = {
        {coords = vector3(-1000.0, -2800.0, 20.0), bank = 'NCB', city = 'الرياض'},
        {coords = vector3(-1200.0, -2600.0, 20.0), bank = 'RAJHI', city = 'الرياض'},
        {coords = vector3(1300.0, 1000.0, 114.0), bank = 'NCB', city = 'جدة'},
        {coords = vector3(1450.0, 1150.0, 114.0), bank = 'RAJHI', city = 'جدة'},
        {coords = vector3(-3000.0, 1000.0, 12.0), bank = 'NCB', city = 'الدمام'}
    },
    
    -- Shops
    shops = {
        {
            name = 'العثيم',
            nameEN = 'Othaim Markets',
            type = 'supermarket',
            coords = vector3(-1150.0, -2650.0, 20.0),
            city = 'الرياض',
            items = {
                {name = 'خبز', nameEN = 'bread', price = 5},
                {name = 'حليب', nameEN = 'milk', price = 8},
                {name = 'أرز', nameEN = 'rice', price = 15},
                {name = 'دجاج', nameEN = 'chicken', price = 25},
                {name = 'لحم', nameEN = 'meat', price = 40},
                {name = 'تمر', nameEN = 'dates', price = 20},
                {name = 'قهوة عربية', nameEN = 'arabic_coffee', price = 30}
            }
        },
        {
            name = 'صيدلية النهدي',
            nameEN = 'Nahdi Pharmacy',
            type = 'pharmacy',
            coords = vector3(-1080.0, -2720.0, 20.0),
            city = 'الرياض',
            items = {
                {name = 'مسكن ألم', nameEN = 'painkiller', price = 15},
                {name = 'مضاد حيوي', nameEN = 'antibiotic', price = 25},
                {name = 'فيتامينات', nameEN = 'vitamins', price = 35},
                {name = 'شامبو', nameEN = 'shampoo', price = 20}
            }
        },
        {
            name = 'محطة بنزين أرامكو',
            nameEN = 'Aramco Gas Station',
            type = 'fuel',
            coords = vector3(-1000.0, -2700.0, 20.0),
            city = 'الرياض',
            items = {
                {name = 'بنزين 95', nameEN = 'fuel_95', price = 2.18},
                {name = 'بنزين 91', nameEN = 'fuel_91', price = 2.04},
                {name = 'ديزل', nameEN = 'diesel', price = 0.75}
            }
        }
    },
    
    -- Business Types
    business_types = {
        {
            name = 'مطعم',
            nameEN = 'restaurant',
            license_cost = 15000,
            monthly_cost = 2000,
            max_employees = 20,
            profit_margin = 0.30
        },
        {
            name = 'محل ملابس',
            nameEN = 'clothing_store',
            license_cost = 10000,
            monthly_cost = 1500,
            max_employees = 10,
            profit_margin = 0.40
        },
        {
            name = 'ورشة سيارات',
            nameEN = 'car_repair',
            license_cost = 20000,
            monthly_cost = 3000,
            max_employees = 15,
            profit_margin = 0.35
        },
        {
            name = 'صيدلية',
            nameEN = 'pharmacy',
            license_cost = 25000,
            monthly_cost = 2500,
            max_employees = 8,
            profit_margin = 0.25
        }
    },
    
    -- Property Prices
    property_prices = {
        apartment = {min = 200000, max = 500000},
        house = {min = 500000, max = 2000000},
        villa = {min = 2000000, max = 10000000},
        office = {min = 300000, max = 1500000},
        shop = {min = 400000, max = 2500000},
        warehouse = {min = 800000, max = 5000000}
    },
    
    -- Investment Options
    investments = {
        {
            name = 'صندوق الاستثمارات العامة',
            nameEN = 'Public Investment Fund',
            min_amount = 10000,
            return_rate = 0.08,
            risk_level = 'medium'
        },
        {
            name = 'أسهم أرامكو',
            nameEN = 'Aramco Stocks',
            min_amount = 5000,
            return_rate = 0.06,
            risk_level = 'low'
        },
        {
            name = 'صناديق المرابحة',
            nameEN = 'Murabaha Funds',
            min_amount = 1000,
            return_rate = 0.04,
            risk_level = 'low'
        }
    },
    
    -- Insurance Types
    insurance = {
        health = {
            name = 'التأمين الصحي',
            nameEN = 'Health Insurance',
            monthly_cost = 200,
            coverage = 0.80
        },
        vehicle = {
            name = 'تأمين المركبات',
            nameEN = 'Vehicle Insurance',
            monthly_cost = 150,
            coverage = 0.70
        },
        property = {
            name = 'تأمين الممتلكات',
            nameEN = 'Property Insurance',
            monthly_cost = 100,
            coverage = 0.90
        }
    }
}

-- Exports
exports {
    'GetPlayerMoney',
    'SetPlayerMoney',
    'AddPlayerMoney',
    'RemovePlayerMoney',
    'TransferMoney',
    'GetBankBalance',
    'DepositMoney',
    'WithdrawMoney',
    'PaySalary',
    'CalculateTax',
    'CalculateZakat',
    'PayTax',
    'PayZakat',
    'GetBusinessInfo',
    'RegisterBusiness',
    'BuyProperty',
    'SellProperty',
    'GetPropertyValue',
    'InvestMoney',
    'GetInvestmentReturn',
    'BuyInsurance',
    'ClaimInsurance',
    'GetEconomyStats',
    'FormatCurrency'
}
