#!/bin/bash

# Saudi FiveM Server Launcher for Linux
# سيرفر فايف ام السعودي - لينكس

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Clear screen
clear

echo -e "${GREEN}"
echo "========================================"
echo "    Saudi FiveM Server Launcher"
echo "    سيرفر فايف ام السعودي"
echo "========================================"
echo -e "${NC}"

# Check if FXServer exists
if [ ! -f "FXServer" ]; then
    echo -e "${RED}[ERROR] FXServer not found!${NC}"
    echo -e "${RED}[خطأ] لم يتم العثور على FXServer!${NC}"
    echo ""
    echo "Please download FiveM server files from:"
    echo "يرجى تحميل ملفات سيرفر فايف ام من:"
    echo "https://runtime.fivem.net/artifacts/fivem/build_proot_linux/master/"
    echo ""
    exit 1
fi

# Check if server.cfg exists
if [ ! -f "server.cfg" ]; then
    echo -e "${RED}[ERROR] server.cfg not found!${NC}"
    echo -e "${RED}[خطأ] لم يتم العثور على server.cfg!${NC}"
    echo ""
    exit 1
fi

# Make FXServer executable
chmod +x FXServer

# Display server information
echo -e "${BLUE}[INFO] Starting Saudi FiveM Server...${NC}"
echo -e "${BLUE}[معلومات] بدء تشغيل السيرفر السعودي...${NC}"
echo ""
echo "Server Name: Saudi Arabia Roleplay Server"
echo "اسم السيرفر: سيرفر المملكة العربية السعودية"
echo ""
echo "Database: MongoDB Atlas"
echo "قاعدة البيانات: مونقو دي بي أطلس"
echo ""
echo "Features:"
echo "المميزات:"
echo "- Saudi localization / التوطين السعودي"
echo "- Arabic UI / واجهة عربية"
echo "- Prayer times / أوقات الصلاة"
echo "- Saudi jobs / وظائف سعودية"
echo "- Saudi vehicles / مركبات سعودية"
echo "- Saudi banking / نظام مصرفي سعودي"
echo "- Government services / خدمات حكومية"
echo "- Saudi identity system / نظام الهوية السعودية"
echo ""

# Set environment variables
export FIVEM_USE_SYSTEM_RESOURCES=1
export FIVEM_SKIP_RESOURCE_SCANNER=1

# Function to start server
start_server() {
    echo -e "${GREEN}[INFO] Launching FiveM Server...${NC}"
    echo -e "${GREEN}[معلومات] تشغيل سيرفر فايف ام...${NC}"
    echo ""
    
    # Start the server
    ./FXServer +exec server.cfg
    
    # If server stops, show message
    echo ""
    echo -e "${YELLOW}========================================${NC}"
    echo -e "${YELLOW}Server has stopped / توقف السيرفر${NC}"
    echo -e "${YELLOW}========================================${NC}"
    echo ""
    
    # Ask if user wants to restart
    echo -e "${BLUE}Do you want to restart the server? (y/n)${NC}"
    echo -e "${BLUE}هل تريد إعادة تشغيل السيرفر؟ (y/n)${NC}"
    read -r restart
    
    if [ "$restart" = "y" ] || [ "$restart" = "Y" ] || [ "$restart" = "yes" ] || [ "$restart" = "YES" ]; then
        echo ""
        echo -e "${GREEN}Restarting server...${NC}"
        echo -e "${GREEN}إعادة تشغيل السيرفر...${NC}"
        sleep 2
        start_server
    else
        echo ""
        echo -e "${BLUE}Server shutdown complete.${NC}"
        echo -e "${BLUE}تم إيقاف السيرفر.${NC}"
        exit 0
    fi
}

# Function to handle Ctrl+C
cleanup() {
    echo ""
    echo -e "${YELLOW}[INFO] Shutting down server...${NC}"
    echo -e "${YELLOW}[معلومات] إيقاف السيرفر...${NC}"
    exit 0
}

# Set trap for Ctrl+C
trap cleanup SIGINT

# Check system requirements
echo -e "${BLUE}[INFO] Checking system requirements...${NC}"
echo -e "${BLUE}[معلومات] فحص متطلبات النظام...${NC}"

# Check available memory
MEMORY=$(free -m | awk 'NR==2{printf "%.1f", $3*100/$2 }')
echo "Memory usage: ${MEMORY}%"
echo "استخدام الذاكرة: ${MEMORY}%"

# Check disk space
DISK=$(df -h | awk '$NF=="/"{printf "%s", $5}')
echo "Disk usage: ${DISK}"
echo "استخدام القرص: ${DISK}"

# Check if MongoDB connection string is set
if grep -q "YOUR_MONGODB_CONNECTION_HERE" server.cfg; then
    echo -e "${YELLOW}[WARNING] MongoDB connection string not configured!${NC}"
    echo -e "${YELLOW}[تحذير] لم يتم تكوين رابط اتصال MongoDB!${NC}"
fi

# Check if license key is set
if grep -q "YOUR_LICENSE_KEY_HERE" server.cfg; then
    echo -e "${YELLOW}[WARNING] FiveM license key not configured!${NC}"
    echo -e "${YELLOW}[تحذير] لم يتم تكوين مفتاح ترخيص FiveM!${NC}"
fi

echo ""

# Start the server
start_server
