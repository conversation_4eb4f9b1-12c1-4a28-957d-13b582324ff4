-- MongoDB Server Script for Saudi FiveM Server
-- سكريبت خادم مونقو دي بي للسيرفر السعودي

local MongoDB = {}
local isConnected = false
local connectionString = GetConvar('mongodb_connection_string', '')
local databaseName = GetConvar('mongodb_database', 'saudi_fivem_server')

-- Saudi specific configurations
local saudiConfig = {
    timezone = 'Asia/Riyadh',
    locale = 'ar-SA',
    currency = 'SAR',
    country_code = 'SA',
    phone_prefix = '+966',
    id_format = '1XXXXXXXXX',
    iqama_format = '2XXXXXXXXX'
}

-- Initialize MongoDB connection
Citizen.CreateThread(function()
    if connectionString ~= '' then
        -- Initialize MongoDB connection here
        print('^2[MongoDB] ^7Connecting to Saudi FiveM Database...')
        print('^2[MongoDB] ^7اتصال بقاعدة البيانات السعودية...')
        
        -- Simulate connection (replace with actual MongoDB connection code)
        Citizen.Wait(2000)
        isConnected = true
        
        print('^2[MongoDB] ^7Successfully connected to database!')
        print('^2[MongoDB] ^7تم الاتصال بقاعدة البيانات بنجاح!')
        
        -- Create indexes for Saudi collections
        CreateSaudiIndexes()
        
        -- Initialize Saudi data
        InitializeSaudiData()
    else
        print('^1[MongoDB] ^7No connection string provided!')
        print('^1[MongoDB] ^7لم يتم توفير رابط الاتصال!')
    end
end)

-- Create indexes for better performance
function CreateSaudiIndexes()
    print('^3[MongoDB] ^7Creating Saudi-specific indexes...')
    print('^3[MongoDB] ^7إنشاء فهارس خاصة بالسعودية...')
    
    -- User indexes
    exports.mongodb:createIndex('users', {steam_id = 1}, {unique = true})
    exports.mongodb:createIndex('users', {saudi_id = 1}, {unique = true, sparse = true})
    exports.mongodb:createIndex('users', {iqama_id = 1}, {unique = true, sparse = true})
    
    -- Character indexes
    exports.mongodb:createIndex('characters', {user_id = 1})
    exports.mongodb:createIndex('characters', {phone_number = 1}, {unique = true, sparse = true})
    
    -- Vehicle indexes
    exports.mongodb:createIndex('vehicles', {owner_id = 1})
    exports.mongodb:createIndex('vehicles', {plate = 1}, {unique = true})
    
    -- Saudi ID indexes
    exports.mongodb:createIndex('saudi_national_ids', {id_number = 1}, {unique = true})
    exports.mongodb:createIndex('saudi_national_ids', {user_id = 1}, {unique = true})
    
    -- Prayer times indexes
    exports.mongodb:createIndex('prayer_times', {date = 1, city = 1}, {unique = true})
    
    -- Hajj permits indexes
    exports.mongodb:createIndex('hajj_permits', {user_id = 1, year = 1}, {unique = true})
    
    -- Driving license indexes
    exports.mongodb:createIndex('saudi_driving_licenses', {license_number = 1}, {unique = true})
    exports.mongodb:createIndex('saudi_driving_licenses', {user_id = 1}, {unique = true})
    
    print('^2[MongoDB] ^7Saudi indexes created successfully!')
    print('^2[MongoDB] ^7تم إنشاء الفهارس السعودية بنجاح!')
end

-- Initialize Saudi-specific data
function InitializeSaudiData()
    print('^3[MongoDB] ^7Initializing Saudi data...')
    print('^3[MongoDB] ^7تهيئة البيانات السعودية...')
    
    -- Initialize Saudi cities
    InitializeSaudiCities()
    
    -- Initialize prayer times
    InitializePrayerTimes()
    
    -- Initialize government services
    InitializeGovernmentServices()
    
    -- Initialize Saudi jobs
    InitializeSaudiJobs()
    
    print('^2[MongoDB] ^7Saudi data initialized successfully!')
    print('^2[MongoDB] ^7تم تهيئة البيانات السعودية بنجاح!')
end

-- Initialize Saudi cities
function InitializeSaudiCities()
    local cities = {
        {name = 'الرياض', name_en = 'Riyadh', coordinates = {x = -1037.0, y = -2737.0, z = 20.2}},
        {name = 'جدة', name_en = 'Jeddah', coordinates = {x = 1400.0, y = 1100.0, z = 114.0}},
        {name = 'الدمام', name_en = 'Dammam', coordinates = {x = -3000.0, y = 1000.0, z = 12.0}},
        {name = 'مكة المكرمة', name_en = 'Mecca', coordinates = {x = 2000.0, y = 3000.0, z = 48.0}},
        {name = 'المدينة المنورة', name_en = 'Medina', coordinates = {x = -2000.0, y = 2500.0, z = 33.0}},
        {name = 'الطائف', name_en = 'Taif', coordinates = {x = 1500.0, y = 2000.0, z = 1500.0}},
        {name = 'بريدة', name_en = 'Buraidah', coordinates = {x = -500.0, y = -1500.0, z = 25.0}},
        {name = 'تبوك', name_en = 'Tabuk', coordinates = {x = -2500.0, y = 3500.0, z = 850.0}},
        {name = 'حائل', name_en = 'Hail', coordinates = {x = -1500.0, y = 500.0, z = 1000.0}},
        {name = 'أبها', name_en = 'Abha', coordinates = {x = 3000.0, y = 1500.0, z = 2200.0}}
    }
    
    for _, city in ipairs(cities) do
        exports.mongodb:insertOne('saudi_cities', city, function(success, result)
            if success then
                print('^2[MongoDB] ^7City ' .. city.name .. ' initialized')
            end
        end)
    end
end

-- Initialize prayer times for major Saudi cities
function InitializePrayerTimes()
    local today = os.date('%Y-%m-%d')
    local prayerTimes = {
        {
            city = 'الرياض',
            date = today,
            fajr = '05:30',
            sunrise = '06:45',
            dhuhr = '12:15',
            asr = '15:45',
            maghrib = '18:30',
            isha = '20:00'
        },
        {
            city = 'جدة',
            date = today,
            fajr = '05:45',
            sunrise = '07:00',
            dhuhr = '12:30',
            asr = '16:00',
            maghrib = '18:45',
            isha = '20:15'
        },
        {
            city = 'الدمام',
            date = today,
            fajr = '05:15',
            sunrise = '06:30',
            dhuhr = '12:00',
            asr = '15:30',
            maghrib = '18:15',
            isha = '19:45'
        }
    }
    
    for _, prayer in ipairs(prayerTimes) do
        exports.mongodb:insertOne('prayer_times', prayer, function(success, result)
            if success then
                print('^2[MongoDB] ^7Prayer times for ' .. prayer.city .. ' initialized')
            end
        end)
    end
end

-- Initialize government services
function InitializeGovernmentServices()
    local services = {
        {name = 'وزارة الداخلية', name_en = 'Ministry of Interior', type = 'government', phone = '999'},
        {name = 'وزارة الصحة', name_en = 'Ministry of Health', type = 'government', phone = '997'},
        {name = 'وزارة النقل', name_en = 'Ministry of Transport', type = 'government', phone = '993'},
        {name = 'الهلال الأحمر', name_en = 'Red Crescent', type = 'emergency', phone = '997'},
        {name = 'الدفاع المدني', name_en = 'Civil Defense', type = 'emergency', phone = '998'},
        {name = 'شرطة المرور', name_en = 'Traffic Police', type = 'police', phone = '993'}
    }
    
    for _, service in ipairs(services) do
        exports.mongodb:insertOne('government_services', service, function(success, result)
            if success then
                print('^2[MongoDB] ^7Service ' .. service.name .. ' initialized')
            end
        end)
    end
end

-- Initialize Saudi jobs
function InitializeSaudiJobs()
    local jobs = {
        {name = 'شرطة', name_en = 'police', salary = 3000, type = 'government'},
        {name = 'إسعاف', name_en = 'ambulance', salary = 2500, type = 'government'},
        {name = 'إطفاء', name_en = 'fire', salary = 2500, type = 'government'},
        {name = 'تاكسي', name_en = 'taxi', salary = 1500, type = 'private'},
        {name = 'أرامكو', name_en = 'aramco', salary = 5000, type = 'government'},
        {name = 'سابك', name_en = 'sabic', salary = 4000, type = 'government'},
        {name = 'معلم', name_en = 'teacher', salary = 3500, type = 'government'},
        {name = 'طبيب', name_en = 'doctor', salary = 6000, type = 'government'},
        {name = 'مهندس', name_en = 'engineer', salary = 4500, type = 'private'},
        {name = 'محامي', name_en = 'lawyer', salary = 4000, type = 'private'}
    }
    
    for _, job in ipairs(jobs) do
        exports.mongodb:insertOne('saudi_jobs', job, function(success, result)
            if success then
                print('^2[MongoDB] ^7Job ' .. job.name .. ' initialized')
            end
        end)
    end
end

-- Saudi ID validation function
function ValidateSaudiID(id)
    if not id or type(id) ~= 'string' then
        return false
    end
    
    -- Saudi ID should be 10 digits starting with 1
    if not string.match(id, '^1%d%d%d%d%d%d%d%d%d$') then
        return false
    end
    
    -- Additional validation logic can be added here
    return true
end

-- Iqama ID validation function
function ValidateIqamaID(id)
    if not id or type(id) ~= 'string' then
        return false
    end
    
    -- Iqama ID should be 10 digits starting with 2
    if not string.match(id, '^2%d%d%d%d%d%d%d%d%d$') then
        return false
    end
    
    return true
end

-- Saudi phone number validation
function ValidatePhoneNumber(phone)
    if not phone or type(phone) ~= 'string' then
        return false
    end
    
    -- Saudi phone numbers: +966XXXXXXXXX or 05XXXXXXXX
    if string.match(phone, '^%+966[5-9]%d%d%d%d%d%d%d%d$') or 
       string.match(phone, '^05%d%d%d%d%d%d%d%d$') then
        return true
    end
    
    return false
end

-- IBAN validation for Saudi banks
function ValidateIBAN(iban)
    if not iban or type(iban) ~= 'string' then
        return false
    end
    
    -- Saudi IBAN format: SA followed by 22 digits
    if string.match(iban, '^SA%d%d%d%d%d%d%d%d%d%d%d%d%d%d%d%d%d%d%d%d%d%d$') then
        return true
    end
    
    return false
end

-- Get prayer times for a specific city and date
function GetPrayerTimes(city, date)
    if not date then
        date = os.date('%Y-%m-%d')
    end
    
    exports.mongodb:findOne('prayer_times', {city = city, date = date}, function(success, result)
        if success and result then
            return result
        else
            -- Return default prayer times for Riyadh if not found
            return {
                fajr = '05:30',
                sunrise = '06:45',
                dhuhr = '12:15',
                asr = '15:45',
                maghrib = '18:30',
                isha = '20:00'
            }
        end
    end)
end

-- Calculate Zakat (Islamic tax)
function CalculateZakat(amount)
    local nisab = 85 * 4.25 -- 85 grams of gold (current price)
    local zakatRate = 0.025 -- 2.5%
    
    if amount >= nisab then
        return amount * zakatRate
    else
        return 0
    end
end

-- Export functions
exports('isConnected', function()
    return isConnected
end)

exports('validateSaudiID', ValidateSaudiID)
exports('validateIqamaID', ValidateIqamaID)
exports('validatePhoneNumber', ValidatePhoneNumber)
exports('validateIBAN', ValidateIBAN)
exports('getPrayerTimes', GetPrayerTimes)
exports('calculateZakat', CalculateZakat)

-- Database operation exports
exports('insertOne', function(collection, document, callback)
    if not isConnected then
        if callback then callback(false, 'Database not connected') end
        return
    end
    
    -- Add Saudi metadata
    document.created_at = os.time()
    document.server_region = 'SA'
    document.timezone = 'Asia/Riyadh'
    
    -- Simulate database operation
    Citizen.SetTimeout(100, function()
        if callback then callback(true, {insertedId = 'generated_id'}) end
    end)
end)

exports('findOne', function(collection, query, callback)
    if not isConnected then
        if callback then callback(false, 'Database not connected') end
        return
    end
    
    -- Simulate database operation
    Citizen.SetTimeout(100, function()
        if callback then callback(true, {}) end
    end)
end)

exports('updateOne', function(collection, query, update, callback)
    if not isConnected then
        if callback then callback(false, 'Database not connected') end
        return
    end
    
    -- Add Saudi metadata
    update['$set'] = update['$set'] or {}
    update['$set'].updated_at = os.time()
    update['$set'].server_region = 'SA'
    
    -- Simulate database operation
    Citizen.SetTimeout(100, function()
        if callback then callback(true, {modifiedCount = 1}) end
    end)
end)

exports('deleteOne', function(collection, query, callback)
    if not isConnected then
        if callback then callback(false, 'Database not connected') end
        return
    end
    
    -- Simulate database operation
    Citizen.SetTimeout(100, function()
        if callback then callback(true, {deletedCount = 1}) end
    end)
end)

-- Event handlers
RegisterServerEvent('mongodb:getSaudiData')
AddEventHandler('mongodb:getSaudiData', function(dataType)
    local source = source
    
    if dataType == 'cities' then
        exports.mongodb:findMany('saudi_cities', {}, function(success, result)
            TriggerClientEvent('mongodb:receiveSaudiData', source, 'cities', result)
        end)
    elseif dataType == 'prayer_times' then
        local today = os.date('%Y-%m-%d')
        exports.mongodb:findMany('prayer_times', {date = today}, function(success, result)
            TriggerClientEvent('mongodb:receiveSaudiData', source, 'prayer_times', result)
        end)
    elseif dataType == 'government_services' then
        exports.mongodb:findMany('government_services', {}, function(success, result)
            TriggerClientEvent('mongodb:receiveSaudiData', source, 'government_services', result)
        end)
    end
end)

print('^2[MongoDB] ^7Saudi FiveM MongoDB wrapper loaded successfully!')
print('^2[MongoDB] ^7تم تحميل مكتبة مونقو دي بي للسيرفر السعودي بنجاح!')
